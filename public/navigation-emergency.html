<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SlotAI - Emergency Navigation</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #f5f5f5;
      color: #333;
      margin: 0;
      padding: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 800px;
      margin: 40px auto;
      padding: 20px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 30px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }
    .warning {
      background-color: #FEF2F2;
      color: #DC2626;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 30px;
      border-left: 4px solid #DC2626;
    }
    .navigation-links {
      display: flex;
      flex-direction: column;
      gap: 15px;
      margin-bottom: 30px;
    }
    .nav-link {
      display: block;
      padding: 15px;
      background-color: #3B82F6;
      color: white;
      border-radius: 8px;
      text-decoration: none;
      text-align: center;
      font-weight: 500;
      transition: all 0.2s ease;
    }
    .nav-link:hover {
      background-color: #2563EB;
      transform: translateY(-2px);
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .nav-link.reset {
      background-color: #4B5563;
    }
    .nav-link.reset:hover {
      background-color: #374151;
    }
    .step-description {
      font-size: 14px;
      color: rgba(255,255,255,0.9);
      margin-top: 5px;
    }
    .info {
      background-color: #EFF6FF;
      color: #1E40AF;
      padding: 15px;
      border-radius: 8px;
      margin-top: 30px;
      border-left: 4px solid #3B82F6;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>SlotAI Emergency Navigation</h1>
    
    <div class="warning">
      <p><strong>Navigation Issue Detected:</strong> You're seeing this page because there was a problem with the normal navigation flow in the application.</p>
      <p>Please use one of the links below to continue your work. Your progress has been saved.</p>
    </div>
    
    <div class="navigation-links">
      <a href="/" class="nav-link reset">
        Restart Application
        <div class="step-description">Go back to the beginning and start fresh</div>
      </a>
      
      <a href="/step1-2-direct.html" class="nav-link">
        Step 2: Game Type Selection
        <div class="step-description">Choose the type of slot game (Classic Reels, Ways to Win, etc.)</div>
      </a>
      
      <a href="/step2-3-direct.html" class="nav-link">
        Step 3: Reel Configuration
        <div class="step-description">Configure grid layout and pay mechanism</div>
      </a>
      
      <a href="/?step=3&force=true" class="nav-link">
        Step 4: Symbol Generation
        <div class="step-description">Create and customize game symbols</div>
      </a>
      
      <a href="/?step=4&force=true" class="nav-link">
        Step 5: Background Creator
        <div class="step-description">Design the game background</div>
      </a>
    </div>
    
    <div class="info">
      <p><strong>Need more help?</strong> If you continue to experience navigation issues, try clearing your browser cache and cookies, then restart the application.</p>
      <p>All your selections are safely stored and will be recovered when you return to the application.</p>
    </div>
  </div>
  
  <script>
    // Track the original location that brought the user here
    window.addEventListener('DOMContentLoaded', () => {
      const previousPage = document.referrer;
      console.log('User arrived from:', previousPage);
      
      // Record navigation issue for debugging
      try {
        localStorage.setItem('slotai_navigation_issue', JSON.stringify({
          timestamp: Date.now(),
          previousPage: previousPage,
          userAgent: navigator.userAgent
        }));
      } catch (e) {
        console.error('Could not save navigation data:', e);
      }
    });
  </script>
</body>
</html>