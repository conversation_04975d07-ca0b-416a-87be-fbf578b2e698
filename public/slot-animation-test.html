<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SlotAI Reel Animation Test</title>
  <script type="module">
    import { createRoot } from 'react-dom/client';
    import React from 'react';
    import { SlotMachineDemo } from '../src/components/slot-visualization';

    document.addEventListener('DOMContentLoaded', () => {
      const container = document.getElementById('app');
      const root = createRoot(container);
      root.render(React.createElement(SlotMachineDemo));
    });
  </script>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, 
                  "Helvetica Neue", Arial, sans-serif;
      background-color: #f9fafb;
      margin: 0;
      padding: 0;
    }
  </style>
</head>
<body>
  <div id="app"></div>
</body>
</html>