<!DOCTYPE html>
<html>
<head>
    <title>Clearing Cache...</title>
</head>
<body>
    <h1>Clearing Cache...</h1>
    <script>
        // Clear all caches
        if ('caches' in window) {
            caches.keys().then(names => {
                names.forEach(name => {
                    caches.delete(name);
                    console.log('Deleted cache:', name);
                });
            });
        }
        
        // Clear local storage
        localStorage.clear();
        
        // Clear session storage
        sessionStorage.clear();
        
        // Redirect to main page after clearing
        setTimeout(() => {
            window.location.href = '/';
        }, 1000);
    </script>
</body>
</html>