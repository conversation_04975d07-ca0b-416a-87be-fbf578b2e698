<!DOCTYPE html>
<html>
<head>
  <title>Direct Step 2 Access</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    
    h1 {
      color: #E60012;
      text-align: center;
      margin-bottom: 30px;
    }
    
    .step {
      background: #f5f5f5;
      border: 1px solid #ddd;
      padding: 20px;
      margin-bottom: 20px;
      border-radius: 5px;
    }
    
    .step h2 {
      margin-top: 0;
      border-bottom: 1px solid #ddd;
      padding-bottom: 10px;
      color: #333;
    }
    
    button {
      background: #E60012;
      color: white;
      border: none;
      padding: 10px 20px;
      font-size: 16px;
      cursor: pointer;
      margin: 10px 0;
      border-radius: 5px;
    }
    
    button:hover {
      background: #cc0000;
    }
    
    code {
      background: #333;
      color: #fff;
      padding: 15px;
      display: block;
      overflow-x: auto;
      line-height: 1.4;
      margin: 15px 0;
      border-radius: 5px;
    }
    
    .important {
      background-color: #ffeaea;
      border-left: 5px solid #E60012;
      padding: 10px 15px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <h1>Direct Access to Step 2 (Game Type Selection)</h1>
  
  <div class="important">
    <p><strong>Current state:</strong> You are stuck on the Theme Selection screen (Step 1) and can't move to Step 2.</p>
    <p>This page provides several alternative solutions to bypass the navigation issue.</p>
  </div>

  <div class="step">
    <h2>Method 1: Simplest Console Command</h2>
    <p>Open your browser's console with F12, then copy and paste this single line:</p>
    <code>useGameStore.getState().setStep(1)</code>
    <button onclick="navigator.clipboard.writeText('useGameStore.getState().setStep(1)')">Copy to Clipboard</button>
  </div>

  <div class="step">
    <h2>Method 2: URL-Based Navigation</h2>
    <p>Click the button below to navigate directly to Step 2 using URL parameters:</p>
    <button onclick="window.location.href='/?step=1&force=true&t=' + Date.now()">Go to Step 2 (Game Type)</button>
  </div>

  <div class="step">
    <h2>Method 3: Complete Reset</h2>
    <p>This will completely reset the application data and restart at Step 2:</p>
    <button onclick="resetAndNavigate()">Reset App &amp; Go to Step 2</button>
  </div>

  <div class="step">
    <h2>Method 4: Full Console Script</h2>
    <p>Open your browser's console (F12), then copy and paste this script:</p>
    <code>
(function() {
  // Get store
  const store = window.useGameStore.getState();
  console.log('Current step:', store.currentStep);
  
  // Force theme and game ID if needed
  if (!store.config.theme?.selectedThemeId || !store.config.gameId) {
    store.updateConfig({
      gameId: 'emergency_' + Date.now(),
      displayName: 'Emergency Game',
      theme: {
        mainTheme: 'Default Theme',
        selectedThemeId: 'default-theme',
        colors: {
          primary: '#E60012',
          secondary: '#0052cc',
          accent: '#ff6600',
          background: '#ffffff'
        }
      }
    });
  }
  
  // Force step change
  store.setStep(1);
  console.log('Navigation command sent!');
  
  // Verify the change worked
  setTimeout(() => {
    console.log('New step:', window.useGameStore.getState().currentStep);
  }, 500);
})();
    </code>
    <button onclick="navigator.clipboard.writeText(`(function() {
  // Get store
  const store = window.useGameStore.getState();
  console.log('Current step:', store.currentStep);
  
  // Force theme and game ID if needed
  if (!store.config.theme?.selectedThemeId || !store.config.gameId) {
    store.updateConfig({
      gameId: 'emergency_' + Date.now(),
      displayName: 'Emergency Game',
      theme: {
        mainTheme: 'Default Theme',
        selectedThemeId: 'default-theme',
        colors: {
          primary: '#E60012',
          secondary: '#0052cc',
          accent: '#ff6600',
          background: '#ffffff'
        }
      }
    });
  }
  
  // Force step change
  store.setStep(1);
  console.log('Navigation command sent!');
  
  // Verify the change worked
  setTimeout(() => {
    console.log('New step:', window.useGameStore.getState().currentStep);
  }, 500);
})();`)">Copy to Clipboard</button>
  </div>

  <script>
    function resetAndNavigate() {
      // Clear all localStorage except core items
      const keysToPreserve = ['slotai_logged_in'];
      const preservedValues = {};
      
      // Save values we want to keep
      keysToPreserve.forEach(key => {
        preservedValues[key] = localStorage.getItem(key);
      });
      
      // Clear everything
      localStorage.clear();
      
      // Restore preserved values
      for (const key in preservedValues) {
        if (preservedValues[key]) {
          localStorage.setItem(key, preservedValues[key]);
        }
      }
      
      // Set up direct navigation
      localStorage.setItem('slotai_emergency_nav', 'true');
      localStorage.setItem('slotai_target_step', '1'); 
      localStorage.setItem('slotai_timestamp', Date.now().toString());
      
      // Store required game data
      const emergencyConfig = {
        gameId: 'emergency_' + Date.now(),
        displayName: 'Emergency Game',
        theme: {
          mainTheme: 'Default Theme',
          selectedThemeId: 'default-theme',
          colors: {
            primary: '#E60012',
            secondary: '#0052cc',
            accent: '#ff6600',
            background: '#ffffff'
          }
        }
      };
      
      localStorage.setItem('slotai_emergency_config', JSON.stringify(emergencyConfig));
      
      // Navigate with URL parameters for double certainty
      window.location.href = `/?step=1&force=true&reset=true&t=${Date.now()}`;
    }
  </script>
</body>
</html>