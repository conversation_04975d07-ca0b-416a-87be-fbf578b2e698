<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vite Import Test</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border-radius: 8px;
    }
    h1 {
      color: #333;
      border-bottom: 1px solid #ddd;
      padding-bottom: 10px;
    }
    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .box {
      border: 1px solid #eee;
      padding: 15px;
      border-radius: 8px;
    }
    .image-container {
      width: 200px;
      height: 200px;
      border: 1px solid #ddd;
      background-color: #f9f9f9;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
    }
    .image-container img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
    .log {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      margin-top: 20px;
      font-family: monospace;
      height: 200px;
      overflow: auto;
    }
    button {
      background-color: #4f46e5;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Vite Import Test</h1>
    <p>Testing image imports with Vite's asset handling</p>
    
    <button id="runTestButton">Run Test</button>
    
    <div id="imageContainer" class="grid">
      <!-- Images will be generated here -->
    </div>
    
    <div class="log" id="log">
      <!-- Logs will appear here -->
    </div>
  </div>

  <script type="module">
    const logElement = document.getElementById('log');
    const imageContainer = document.getElementById('imageContainer');
    const runTestButton = document.getElementById('runTestButton');
    
    // Function to log with timestamp
    function log(message, type = 'info') {
      const time = new Date().toLocaleTimeString();
      const color = type === 'error' ? '#e53e3e' : 
                    type === 'success' ? '#38a169' : '#333';
      
      logElement.innerHTML += `<div style="color: ${color}">[${time}] ${message}</div>`;
      logElement.scrollTop = logElement.scrollHeight;
      
      // Also console log
      if (type === 'error') {
        console.error(message);
      } else {
        console.log(message);
      }
    }
    
    // Function to create an image element
    function createImageElement(src, alt, id) {
      const box = document.createElement('div');
      box.className = 'box';
      
      const title = document.createElement('h3');
      title.textContent = alt;
      
      const imageWrapper = document.createElement('div');
      imageWrapper.className = 'image-container';
      
      const img = document.createElement('img');
      img.id = id;
      img.alt = alt;
      
      // Add event listeners
      img.onload = () => {
        log(`✅ Image loaded: ${alt} (${img.naturalWidth}x${img.naturalHeight})`, 'success');
      };
      
      img.onerror = () => {
        log(`❌ Image failed to load: ${alt}`, 'error');
      };
      
      // Set source last to trigger load
      img.src = src;
      
      imageWrapper.appendChild(img);
      box.appendChild(title);
      box.appendChild(imageWrapper);
      
      const path = document.createElement('div');
      path.style.fontSize = '12px';
      path.style.color = '#666';
      path.textContent = `Path: ${src}`;
      box.appendChild(path);
      
      return box;
    }
    
    // Run the tests
    async function runTests() {
      log('Starting Vite image import tests...');
      imageContainer.innerHTML = '';
      
      try {
        // Clear any existing content
        imageContainer.innerHTML = '';
        
        // Test relative path imports
        const testPaths = [
          { path: '/assets/frames/decorations/minimal.png', alt: 'Minimal Decoration', id: 'minimal' },
          { path: '/assets/frames/decorations/decorated.png', alt: 'Decorated Decoration', id: 'decorated' },
          { path: '/assets/frames/styles/cartoon.png', alt: 'Cartoon Style', id: 'cartoon' },
          { path: '/assets/frames/styles/dark.png', alt: 'Dark Style', id: 'dark' },
          { path: '/assets/symbols/high_1.png', alt: 'Symbol (Control)', id: 'symbol' },
          { path: 'https://via.placeholder.com/200', alt: 'External (Control)', id: 'external' }
        ];
        
        // Add timestamp to paths to avoid caching
        const timestamp = Date.now();
        log(`Using timestamp: ${timestamp}`);
        
        testPaths.forEach(item => {
          const src = item.path.includes('http') ? 
            `${item.path}?t=${timestamp}` : 
            `${item.path}?t=${timestamp}`;
          
          const element = createImageElement(src, item.alt, item.id);
          imageContainer.appendChild(element);
        });
        
        // Check if images loaded after a delay
        setTimeout(() => {
          testPaths.forEach(item => {
            const img = document.getElementById(item.id);
            if (img) {
              if (img.complete && img.naturalWidth > 0) {
                log(`DOM check: ${item.alt} is loaded`, 'success');
              } else {
                log(`DOM check: ${item.alt} is not loaded`, 'error');
              }
            }
          });
        }, 2000);
        
      } catch (error) {
        log(`Error running tests: ${error.message}`, 'error');
      }
    }
    
    // Set up event listener
    runTestButton.addEventListener('click', runTests);
    
    // Run tests automatically on load
    document.addEventListener('DOMContentLoaded', runTests);
  </script>
</body>
</html>