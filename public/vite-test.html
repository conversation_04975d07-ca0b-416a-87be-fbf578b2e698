<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vite Image Direct Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        h1 {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .test-header {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .test-desc {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .image-container {
            border: 1px solid #eee;
            padding: 10px;
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 15px;
            background: #f9f9f9;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <h1>Vite Image Loading Test Page</h1>
    <p>This page directly tests image loading in a plain HTML file without React or other JS frameworks.</p>
    
    <div class="test-container">
        <div class="test-header">1. Standard HTML Image (Public Path)</div>
        <div class="test-desc">Testing standard HTML img tag with a path from the public directory</div>
        <div class="image-container">
            <img src="/assets/frames/decorations/minimal.png" alt="Minimal Decoration" width="200" height="200">
            <div>minimal.png</div>
        </div>
        <div class="image-container">
            <img src="/assets/frames/decorations/decorated.png" alt="Decorated" width="200" height="200">
            <div>decorated.png</div>
        </div>
    </div>
    
    <div class="test-container">
        <div class="test-header">2. Standard HTML Image (Symbols)</div>
        <div class="test-desc">Testing with symbol images that are known to work in other contexts</div>
        <div class="image-container">
            <img src="/assets/symbols/high_1.png" alt="High 1" width="200" height="200">
            <div>high_1.png</div>
        </div>
    </div>
    
    <div class="test-container">
        <div class="test-header">3. Background Images (CSS)</div>
        <div class="test-desc">Testing CSS background-image property</div>
        <div class="image-container" style="width:200px; height:200px; background-image: url('/assets/frames/styles/cartoon.png'); background-size: contain; background-repeat: no-repeat; background-position: center;">
        </div>
        <div>cartoon.png (background-image)</div>
    </div>

    <div class="test-container">
        <div class="test-header">4. External Control Image</div>
        <div class="test-desc">External image to verify basic image rendering is working</div>
        <div class="image-container">
            <img src="https://via.placeholder.com/200" alt="External" width="200" height="200">
            <div>placeholder.com/200</div>
        </div>
    </div>
    
    <div class="test-container" id="imageReport">
        <div class="test-header">5. JavaScript Image Testing</div>
        <div class="test-desc">Using JavaScript to test image loading programmatically</div>
        <pre id="results">Running tests...</pre>
        <div id="dynamic-images"></div>
    </div>

    <script>
        // JavaScript image loading tests
        const imagesToTest = [
            '/assets/frames/decorations/minimal.png',
            '/assets/frames/decorations/decorated.png',
            '/assets/frames/styles/cartoon.png',
            '/assets/frames/styles/dark.png',
            '/assets/symbols/high_1.png',
            'https://via.placeholder.com/200'
        ];
        
        const results = [];
        let testsCompleted = 0;
        
        function updateResults() {
            document.getElementById('results').textContent = results.join('\n');
        }
        
        function addDynamicImage(src, success) {
            const container = document.createElement('div');
            container.className = 'image-container';
            
            if (success) {
                const img = document.createElement('img');
                img.src = src;
                img.alt = "Dynamic test";
                img.width = 100;
                img.height = 100;
                container.appendChild(img);
            }
            
            const status = document.createElement('div');
            status.className = success ? 'success' : 'error';
            status.textContent = success ? '✓ Loaded' : '✗ Failed';
            container.appendChild(status);
            
            const path = document.createElement('div');
            path.textContent = src;
            container.appendChild(path);
            
            document.getElementById('dynamic-images').appendChild(container);
        }
        
        function testImage(src) {
            return new Promise((resolve) => {
                const img = new Image();
                
                img.onload = function() {
                    results.push(`✓ SUCCESS: ${src} loaded (${img.naturalWidth}x${img.naturalHeight})`);
                    updateResults();
                    testsCompleted++;
                    addDynamicImage(src, true);
                    resolve(true);
                };
                
                img.onerror = function() {
                    results.push(`✗ ERROR: ${src} failed to load`);
                    updateResults();
                    testsCompleted++;
                    addDynamicImage(src, false);
                    
                    // Try with fetch as a backup
                    fetch(src, { method: 'HEAD' })
                        .then(response => {
                            if (response.ok) {
                                results.push(`  > Fetch shows file exists (status ${response.status}), but image can't be displayed`);
                            } else {
                                results.push(`  > Fetch confirms file doesn't exist (status ${response.status})`);
                            }
                            updateResults();
                        })
                        .catch(error => {
                            results.push(`  > Fetch failed: ${error.message}`);
                            updateResults();
                        });
                    
                    resolve(false);
                };
                
                img.src = src;
            });
        }
        
        // Run all tests
        const runTests = async () => {
            results.push('Testing image loading:');
            updateResults();
            
            for (const src of imagesToTest) {
                await testImage(src);
            }
            
            results.push(`\nCompleted ${testsCompleted} tests.`);
            updateResults();
        };
        
        // Start tests when page loads
        window.addEventListener('load', runTests);
    </script>
</body>
</html>