export type ThemeConfig = {
  mainTheme: string;
  artStyle?: 'cartoon' | 'realistic' | 'pixel' | 'hand-drawn';
  appliedAt?:string;
  colorScheme: string;
  colors?: {
    newColors?:string;
    primary: string;
    secondary: string;
    accent: string;
    background?: string;
  };
  mood: string;
  description: string;
  references: string[];
  includeCardSymbols?: boolean;
  includeWild?: boolean;
  includeScatter?: boolean;
  selectedSymbols?: string[];
  selectedThemeId?:string
  generated?: {
    background: string | null;
    symbols: string[];
    frame: string | null;
  };
};

export type ColorSet = {
  primary: string;
  secondary: string;
  accent: string;
  background?: string;
  newColors?: string;
};

export type BetConfig = {
  min: number;
  max: number;
  increment: number;
  quickOptions?: number[];
  defaultBet?: number;
  maxLines?: number;
};

export type RTPConfig = {
  baseRTP: number;
  bonusRTP: number;
  featureRTP: number;
  variants: {
    low: number;
    medium: number;
    high: number;
  };
  targetRTP?: number;
  volatilityScale?: number; // 1-10 scale
};

export type VolatilityConfig = {
  level: 'low' | 'medium' | 'high';
  variance: number;
  hitRate: number;
  maxWinPotential: number;
  precisionValue?: number; // 1-10 scale
  hitFrequency?: number; // percentage
};

export type ReelsConfig = {
  payMechanism: 'betlines' | 'ways' | 'cluster';
  layout: {
    shape: 'rectangle' | 'square' | 'diamond' | 'hexagon';
    reels: number;
    rows: number;
  };
  betlines: number;
  spinDirection: 'vertical' | 'horizontal';
  cluster?: {
    minSymbols: number;
    diagonalAllowed: boolean;
    payouts: Record<number, number>; // symbolCount -> payout
  };
  symbols: {
    total: number;
    wilds: number;
    scatters: number;
    list: SymbolConfig[];
  };
};

export type SymbolConfig = {
  id: string;
  name: string;
  image: string;
  weight: number;
  isWild?: boolean;
  isScatter?: boolean;
  payouts: Record<number, number>; // symbolCount -> payout
};

export type AudioConfig = {
  backgroundMusic: string;
  spinSound: string;
  winSounds: {
    small: string;
    medium: string;
    big: string;
    mega: string;
  };
  featureSounds: Record<string, string>;
  soundIntensity: 'low' | 'medium' | 'high';
  enableVoiceover: boolean;
};

export type PlayerExperienceConfig = {
  spinSpeed: 'slow' | 'normal' | 'fast' | 'turbo';
  autospinOptions: number[];
  defaultAutospin: number;
  skipAnimations: boolean;
  bigWinThreshold: number; // multiplier of bet
  megaWinThreshold: number; // multiplier of bet
};

export type LocalizationConfig = {
  supportedLanguages: string[];
  defaultLanguage: string;
  supportedCurrencies: string[];
  defaultCurrency: string;
  regionalRestrictions: string[];
};

export type MobileConfig = {
  orientationMode: 'portrait' | 'landscape' | 'both';
  touchControls: {
    swipeToSpin: boolean;
    gestureControls: boolean;
    vibrateOnWin: boolean;
  };
  screenAdaptation: {
    smallScreenLayout: boolean;
    largeButtonsForTouch: boolean;
  };
};

export type AnalyticsConfig = {
  trackEvents: boolean;
  metricsToTrack: string[];
  abTestingEnabled: boolean;
  abTestVariants?: string[];
};

export type CertificationConfig = {
  targetMarkets: string[];
  selectedLab?: string;
  complianceChecklist: Record<string, boolean>;
  testingResults: {
    rtpVerification: boolean;
    functionalTest: boolean;
    securityAudit: boolean;
  };
  regulatoryDocs: string[];
};

export type DistributionConfig = {
  marketplaceListings: string[];
  revenueModel: 'revenue-share' | 'flat-fee' | 'hybrid';
  integrationPlatforms: string[];
  exclusivity: boolean;
};

export type BonusConfig = {
  freeSpins?: {
    enabled: boolean;
    count: number;
    triggers?: number[];
    multipliers?: number[];
    retriggers?: boolean;
  };
  pickAndClick?: {
    enabled: boolean;
    gridSize: [number, number];
    picks: number;
    maxPrize: number;
    extraPicks?: boolean;
    multipliers?: boolean;
  };
  wheel?: {
    enabled: boolean;
    segments: number;
    maxMultiplier: number;
    levelUp?: boolean;
    respin?: boolean;
  };
  holdAndSpin?: {
    enabled: boolean;
    gridSize: [number, number];
    initialRespins: number;
    maxSymbolValue: number;
    resetRespins?: boolean;
    collectAll?: boolean;
  };
  jackpots?: {
    enabled: boolean;
    type: 'fixed' | 'progressive';
    levels: string[];
    trigger: 'random' | 'symbol' | 'bonus';
    values?: Record<string, number>;
  };
  specialFeatures?: {
    expandingWilds?: boolean;
    stickyWilds?: boolean;
    cascadingReels?: boolean;
    bonusWheel?: boolean;
  };
};

export type ApiConfig = {
  baseUrl: string;
  apiKey: string;
  enabled: boolean;
  lastConnected?: string;
  useLocalMock?: boolean;
};

export type GeminiConfig = {
  apiKey: string;
  modelName: string;
  enabled?: boolean;
  lastConnected?: string;
};

export type OpenAIConfig = {
  apiKey: string;
  modelName: string;
  enabled?: boolean;
  lastConnected?: string;
  quality?: 'standard' | 'hd' | 'low' | 'medium' | 'high';
  size?: '256x256' | '512x512' | '1024x1024' | '1024x1792' | '1792x1024';
  style?: 'vivid' | 'natural';
  background?: 'transparent' | 'solid';
};

export type LeonardoConfig = {
  apiKey: string;
  modelId?: string;
  enabled?: boolean;
  lastConnected?: string;
  width?: number;
  height?: number;
  presetStyle?: string;
  promptMagic?: boolean;
  alchemy?: boolean;
  contrastRatio?: number;
  guidanceScale?: number;
  seed?: number;
};

export type FrameConfig = {
  style: 'modern' | 'classic' | 'ornate' | 'minimal' | 'custom';
  borderWidth: number;
  borderRadius: number;
  borderColor: string;
  backgroundColor: string;
  shadowEnabled: boolean;
  shadowColor: string;
  shadowBlur: number;
  frameImage?: string;
  buttons: {
    style: 'round' | 'square' | 'pill';
    color: string;
    textColor: string;
    position: 'bottom' | 'side';
    size: 'small' | 'medium' | 'large';
  };
  customStyles?: Record<string, any>;
};

export type BackgroundConfig = {
  type: 'static' | 'animated';
  style: 'nature' | 'space' | 'abstract' | 'fantasy' | 'urban' | 'custom';
  color: string;
  secondaryColor?: string;
  gradientType?: 'linear' | 'radial';
  gradientDirection?: 'top-to-bottom' | 'left-to-right' | 'diagonal';
  backgroundImage?: string;
  effects?: {
    particles?: boolean;
    particleColor?: string;
    particleCount?: number;
    particleSpeed?: number;
    lightning?: boolean;
    lightningFrequency?: number;
    rain?: boolean;
    rainIntensity?: number;
    snow?: boolean;
    snowIntensity?: number;
    stars?: boolean;
    starCount?: number;
    clouds?: boolean;
    cloudCount?: number;
    dayNightCycle?: boolean;
    cycleLength?: number;
  };
  customSettings?: Record<string, any>;
};

export type WinAnimationConfig = {
  type: 'lightning' | 'sparkle' | 'glow' | 'fire' | 'confetti' | 'coins' | 'custom';
  intensity: number;
  duration: number;
  color: string;
  secondaryColor?: string;
  soundEnabled: boolean;
  particleCount: number;
  particleSize: number;
  particleSpeed: number;
  glowEnabled: boolean;
  glowColor: string;
  glowIntensity: number;
  shakeEnabled: boolean;
  shakeIntensity: number;
  flashEnabled: boolean;
  flashColor: string;
  flashCount: number;
  zoomEnabled: boolean;
  zoomScale: number;
  pathHighlight: boolean;
  highlightColor: string;
  highlightWidth: number;
  customScript?: string;
};

export type WinMultiplierThresholds = {
  smallWin: number;    // Default: 1x (minimum for any win)
  bigWin: number;      // Default: 5x
  megaWin: number;     // Default: 25x  
  superWin: number;    // Default: 100x
};

export type WinTierCalculation = {
  betAmount: number;
  winAmount: number;
  multiplier: number;
  tier: 'small' | 'big' | 'mega' | 'super';
};

export type GameConfig = {
  api?: ApiConfig;
  gemini?: GeminiConfig;
  openai?: OpenAIConfig;
  leonardo?: LeonardoConfig;
  theme?: Partial<ThemeConfig>;
  bet: BetConfig;
  rtp: RTPConfig;
  volatility: VolatilityConfig;
  reels: ReelsConfig;
  bonus?: BonusConfig;
  audio?: AudioConfig;
  playerExperience?: PlayerExperienceConfig;
  localization?: LocalizationConfig;
  mobile?: MobileConfig;
  analytics?: AnalyticsConfig;
  certification?: CertificationConfig;
  distribution?: DistributionConfig;
  gameRules?: {
    helpScreens: string[];
    paytableConfig: Record<string, any>;
  };
  persistSelection?:boolean;
  gameTypeInfo?: {
    id: string;
    title: string;
    description: string;
    features: string;
    selectedAt: string;
};
  // gameTypeInfo: {
  //               id: string,
  //               title: string,
  //               description: string,
  //               features: string,
  //               selectedAt: string,
  //             };
  frame?: FrameConfig;
  background?: BackgroundConfig;
  winAnimation?: WinAnimationConfig;
  winAnimations?: Record<string, any>; // Store for Step 7 win animation effects  
  winMultiplierThresholds?: WinMultiplierThresholds;
  selectedWinAnimationPreset?: string; // Store selected animation preset
  generatedAssets?: {
    winTitles?: Record<string, string>;
    particles?: Record<string, string>;
  };
  titleAssets?: {
    freeSpins?: string;
    bonusGame?: string;
    pickAndClick?: string;
    smallWin?: string;
    bigWin?: string;
    megaWin?: string;
    superWin?: string;
    gameOver?: string;
    congratulations?: string;
  };
  titleAssetsStyle?: string;
  visualJourney?: {
    currentStep: number;
    totalSteps: number;
    progress: number;
    completedSteps: Record<string, boolean>;
  };
  gameId?: string;
  loadedFromApi?: boolean;
  lastLoaded?: string;
  selectedGameType?: string;
  isClone?: boolean;
  clonedFrom?: string;
  clonedAt?: string;
  displayName?:string;
  gameTheme?:string;
  splashScreen?: {
    enabled?: boolean;
    gameTitle?: string;
    gameSubtitle?: string;
    featureHighlights?: Array<{
      id: string;
      title: string;
      description: string;
      icon: string;
      image: string | null;
      position: { x: number; y: number };
      size: { width: number; height: number };
      backgroundColor: string;
      textColor: string;
    }>;
    splashDuration?: number;
    splashBackground?: string;
    splashTextColor?: string;
  };
  // Reel divider properties
  reelGap?: number;
  reelDividerPosition?: { x: number; y: number };
  reelDividerStretch?: { x: number; y: number };
  aiReelImage?: string | null;
};

// Professional Animation Studio Types
export interface AnimationPreset {
  name: string;
  settings: {
    speed: number;
    blurIntensity: number;
    easing: string;
  };
  description: string;
}

export interface PerformanceMetrics {
  fps: number;
  frameCount: number;
  lastTime: number;
  memoryUsage: number;
  animationComplexity: number;
}

export interface MaskBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export type DeviceProfile = 'mobile' | 'desktop' | 'tablet';
export type MaskPreviewMode = 'normal' | 'overlay' | 'mask-only' | 'before-after';

export interface AnimationWorkspace {
  selectedPreset: string;
  maskEditMode: boolean;
  previewMode: MaskPreviewMode;
  performanceMode: DeviceProfile;
  showEasingCurve: boolean;
}

// Enhanced Animation Studio Types
export interface AISuggestion {
  id: string;
  type: 'performance' | 'ux' | 'accessibility' | 'best-practice';
  severity: 'info' | 'warning' | 'error' | 'success';
  message: string;
  action?: string;
  condition?: string;
  stepSpecific?: boolean;
}

export interface AnimationProfile {
  id: string;
  name: string;
  description: string;
  stepType: 'animation' | 'grid' | 'assets' | 'win-animation' | 'theme' | 'general';
  settings: Record<string, any>;
  isDefault?: boolean;
  isFavorite?: boolean;
  tags?: string[];
  createdAt: Date;
  lastUsed?: Date;
  usageCount?: number;
  author?: string;
  version?: string;
}

export interface UsabilityEvent {
  id: string;
  type: 'click' | 'hover' | 'focus' | 'scroll' | 'error' | 'completion' | 'abandonment';
  element: string;
  stepType: string;
  timestamp: Date;
  duration?: number;
  metadata?: Record<string, any>;
  userAgent?: string;
  viewport?: { width: number; height: number };
}

export interface UsabilityMetrics {
  totalInteractions: number;
  averageTaskTime: number;
  errorRate: number;
  completionRate: number;
  mostUsedFeatures: Array<{ element: string; usage: number }>;
  problemAreas: Array<{ element: string; issues: number }>;
  sessionDuration: number;
  bounceRate: number;
}

export interface DeviceOptimization {
  device: 'mobile' | 'tablet' | 'desktop' | 'low-end';
  maxBlur: number;
  maxEffects: number;
  targetFPS: number;
  memoryLimit: number;
  adaptiveQuality: boolean;
}

export interface NodeBasedAnimation {
  id: string;
  type: 'speed' | 'easing' | 'blur' | 'effect' | 'trigger';
  x: number;
  y: number;
  inputs: NodeConnection[];
  outputs: NodeConnection[];
  parameters: Record<string, any>;
}

export interface NodeConnection {
  sourceNodeId: string;
  targetNodeId: string;
  sourceOutput: string;
  targetInput: string;
}

export interface TimelineKeyframe {
  time: number; // 0-1 representing animation progress
  property: string;
  value: any;
  easing?: string;
}

export interface AnimationTemplate {
  profile: AnimationProfile;
  nodeGraph?: NodeBasedAnimation[];
  timeline?: TimelineKeyframe[];
  deviceOptimizations: Record<string, DeviceOptimization>;
  previewSettings: {
    showGrid: boolean;
    showDebugInfo: boolean;
    realTimeUpdates: boolean;
  };
}