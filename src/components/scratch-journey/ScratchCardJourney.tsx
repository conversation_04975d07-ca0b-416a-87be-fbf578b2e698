import React from 'react';
import { useGameStore } from '../../store';
import { Ticket, Eraser, Sparkles } from 'lucide-react';

const ScratchCardJourney: React.FC = () => {
  const currentStep = useGameStore((state) => state.currentStep);
  const nextStep = useGameStore((state) => state.nextStep);
  const prevStep = useGameStore((state) => state.prevStep);
  
  // Define steps for scratch card creation
  const scratchCardSteps = [
    'Theme Selection',
    'Card Design',
    'Prize Structure',
    'Game Rules',
    'Visual Effects',
    'Test & Export'
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="mb-6 pb-4">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-green-600 text-white rounded-full flex items-center justify-center">
            <Ticket className="w-5 h-5" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Scratch Card Designer</h1>
            <p className="text-gray-600 text-sm">Create interactive scratch card games with custom themes and prize structures</p>
          </div>
        </div>
        
        <div className="p-4 bg-gradient-to-r from-green-50 to-teal-50 rounded-lg border border-green-100">
          <div className="flex items-center gap-2 mb-3">
            <Eraser className="w-4 h-4 text-green-600" />
            <h2 className="font-semibold text-green-800">Scratch Card Creation Journey</h2>
            <Sparkles className="w-4 h-4 text-amber-500" />
          </div>
          
          <div className="flex-1">
            <div className="relative pt-1">
              <div className="flex mb-2 items-center justify-between">
                <div>
                  <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-green-700 bg-green-200">
                    Step {currentStep + 1} of {scratchCardSteps.length}
                  </span>
                </div>
                <div className="text-right">
                  <span className="text-xs font-semibold inline-block text-green-700">
                    {Math.round(((currentStep + 1) / scratchCardSteps.length) * 100)}%
                  </span>
                </div>
              </div>
              <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-green-200">
                <div
                  style={{ width: `${((currentStep + 1) / scratchCardSteps.length) * 100}%` }}
                  className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-green-500 to-teal-500"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-bold text-gray-800 mb-4">{scratchCardSteps[currentStep]}</h2>
        
        {/* Step content goes here */}
        <div className="p-4 bg-blue-50 rounded-lg border border-blue-100 text-blue-800">
          <p className="mb-2 font-medium">This is a prototype of the Scratch Card journey</p>
          <p>Step {currentStep + 1}: {scratchCardSteps[currentStep]} would be implemented here.</p>
          <p className="mt-2 text-sm">Coming soon! This game type is under development.</p>
        </div>
      </div>

      <div className="flex justify-between mt-8">
        <button
          onClick={prevStep}
          disabled={currentStep === 0}
          className={`px-4 py-2 rounded-lg flex items-center gap-1 ${
            currentStep === 0
              ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Previous Step
        </button>
        
        <button
          onClick={nextStep}
          disabled={currentStep === scratchCardSteps.length - 1}
          className={`px-4 py-2 rounded-lg flex items-center gap-1 ${
            currentStep === scratchCardSteps.length - 1
              ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
              : 'bg-green-600 text-white hover:bg-green-700'
          }`}
        >
          Next Step
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default ScratchCardJourney;