# 🎬 Enhanced PIXI Sprite Sheet Component

## Overview

The upgraded `pixiSpriteSheet.tsx` component now integrates with your OpenAI infrastructure to generate animated sprite sheets on-demand. This component combines PIXI.js animation capabilities with AI-powered sprite generation for creating dynamic slot machine symbols.

## Features

### 🤖 AI-Powered Generation
- **OpenAI Integration**: Uses your existing `enhancedOpenaiClient` for sprite generation
- **5x5 Sprite Sheets**: Generates 25-frame animation sequences automatically
- **Multiple Content Types**: Symbol-only, WILD, SCATTER, BONUS, FREE, JACKPOT, text-only
- **Animation Complexity**: Simple, medium, or complex animation styles

### 🎮 PIXI.js Animation
- **Smooth Rendering**: Uses PIXI.AnimatedSprite for optimal performance
- **Customizable Speed**: Adjustable animation speed and loop settings
- **Flexible Grid**: Configurable grid dimensions (default 5x5)
- **Modern Asset Loading**: Uses PIXI.Assets for efficient texture management

### 🎨 User Interface
- **Generation Controls**: Interactive UI for configuring sprite generation
- **Real-time Preview**: Immediate animation playback after generation
- **Status Feedback**: Clear success/error messages and loading states
- **Responsive Design**: Adapts to different container sizes

## Usage

### Basic Usage (Static Sprite Sheet)

```tsx
import PixiAnimatedSymbol from './pixiSpriteSheet';

function MyComponent() {
  return (
    <PixiAnimatedSymbol
      imageUrl="/path/to/sprite-sheet.png"
      width={256}
      height={256}
      animationSpeed={0.2}
    />
  );
}
```

### Advanced Usage (With Generation)

```tsx
import PixiAnimatedSymbol from './pixiSpriteSheet';

function MyComponent() {
  const handleSpriteGenerated = (spriteUrl: string) => {
    console.log('New sprite generated:', spriteUrl);
    // Save to your state management system
  };

  return (
    <PixiAnimatedSymbol
      enableGeneration={true}
      onSpriteGenerated={handleSpriteGenerated}
      width={320}
      height={320}
      animationSpeed={0.15}
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `imageUrl` | `string` | `undefined` | URL to existing sprite sheet |
| `gridCols` | `number` | `5` | Number of columns in sprite grid |
| `gridRows` | `number` | `5` | Number of rows in sprite grid |
| `animationSpeed` | `number` | `0.2` | Animation playback speed |
| `width` | `number` | `256` | Rendered width in pixels |
| `height` | `number` | `256` | Rendered height in pixels |
| `enableGeneration` | `boolean` | `false` | Show generation controls |
| `onSpriteGenerated` | `function` | `undefined` | Callback when sprite is generated |

## Generation Configuration

### Symbol Types
- **Block**: Square format with full background
- **Contour**: Custom shape with transparent background

### Content Types
- **symbol-only**: Pure symbol without text
- **symbol-wild**: Symbol with "WILD" text
- **symbol-scatter**: Symbol with "SCATTER" text
- **symbol-bonus**: Symbol with "BONUS" text
- **symbol-free**: Symbol with "FREE" text
- **symbol-jackpot**: Symbol with "JACKPOT" text
- **text-only**: Text-based animation only

### Animation Complexity
- **Simple**: Gentle floating, subtle glow variations
- **Medium**: Dynamic movement, particles, color shifts
- **Complex**: Elaborate transformations, magical effects

## Integration Examples

### 1. Visual Journey Step
```tsx
// Add to your visual journey as a new step
import Step_SpriteSheetGenerator from './Step_SpriteSheetGenerator';

// Use in your step routing
<Step_SpriteSheetGenerator />
```

### 2. Animation Lab Integration
```tsx
// Integrate with existing animation lab
import PixiAnimatedSymbol from './pixiSpriteSheet';

function AnimationLabStep() {
  return (
    <div>
      <h3>Generate Animated Symbols</h3>
      <PixiAnimatedSymbol
        enableGeneration={true}
        onSpriteGenerated={(url) => {
          // Save to animation lab storage
          saveToAnimationLab(url);
        }}
      />
    </div>
  );
}
```

### 3. Symbol Preview
```tsx
// Use for previewing generated symbols
function SymbolPreview({ spriteUrl }: { spriteUrl: string }) {
  return (
    <PixiAnimatedSymbol
      imageUrl={spriteUrl}
      width={150}
      height={150}
      animationSpeed={0.1}
    />
  );
}
```

## Technical Details

### Dependencies
- **PIXI.js**: For rendering and animation
- **enhancedOpenaiClient**: Your existing OpenAI integration
- **spriteSheetGenerator**: Utility for generating 5x5 sprite sheets

### Performance Considerations
- Uses modern PIXI.Assets for efficient loading
- Proper cleanup on component unmount
- Error handling for failed generations
- Optimized texture management

### Error Handling
- Network failure recovery
- Invalid sprite sheet detection
- User-friendly error messages
- Graceful fallbacks

## File Structure

```
src/components/visual-journey/steps-working/
├── pixiSpriteSheet.tsx           # Main component
├── PixiSpriteSheetDemo.tsx       # Demo/example usage
├── Step_SpriteSheetGenerator.tsx # Visual journey integration
└── README_PixiSpriteSheet.md     # This documentation
```

## Next Steps

1. **Test the Component**: Use `PixiSpriteSheetDemo.tsx` to test functionality
2. **Integrate**: Add to your visual journey or animation lab
3. **Customize**: Modify generation prompts and UI to match your design
4. **Extend**: Add additional content types or animation styles as needed

## Troubleshooting

### Common Issues
- **PIXI.js Version**: Ensure compatibility with your PIXI.js version
- **OpenAI API**: Verify your API key and rate limits
- **Sprite Loading**: Check network connectivity and CORS settings
- **Animation Performance**: Adjust animation speed for smoother playback

### Debug Mode
Enable console logging to see detailed generation and loading information:
```tsx
// Component automatically logs generation progress
// Check browser console for detailed information
```
