import React, { useRef, useEffect } from 'react';
import * as PIXI from 'pixi.js';

/**
 * PixiAnimatedSymbol
 * 
 * Props:
 * - imageUrl: URL or path to the generated sprite sheet (5x5 grid by default)
 * - gridCols: number of columns in the grid (default: 5)
 * - gridRows: number of rows in the grid (default: 5)
 * - animationSpeed: speed for AnimatedSprite (default: 0.2)
 * - width: desired width of the rendered symbol (default: frameWidth)
 * - height: desired height of the rendered symbol (default: frameHeight)
 */
export default function PixiAnimatedSymbol({
  imageUrl,
  gridCols = 5,
  gridRows = 5,
  animationSpeed = 0.2,
  width,
  height,
}) {
  const containerRef = useRef(null);
  const appRef = useRef(null);
  const spriteRef = useRef(null);

  useEffect(() => {
    // Initialize PIXI Application
    const app = new PIXI.Application({
      width: width || 256,
      height: height || 256,
      transparent: true,
      resolution: window.devicePixelRatio || 1,
    });
    appRef.current = app;

    // Append view to container
    containerRef.current.appendChild(app.view);

    // Load and setup animation
    const loader = new PIXI.Loader();
    loader.add('symbolSheet', imageUrl).load((loader, resources) => {
      const baseTexture = resources.symbolSheet.texture.baseTexture;
      const frameWidth = baseTexture.width / gridCols;
      const frameHeight = baseTexture.height / gridRows;
      const textures = [];

      // Slice frames
      for (let row = 0; row < gridRows; row++) {
        for (let col = 0; col < gridCols; col++) {
          textures.push(
            new PIXI.Texture(
              baseTexture,
              new PIXI.Rectangle(col * frameWidth, row * frameHeight, frameWidth, frameHeight)
            )
          );
        }
      }

      // Create AnimatedSprite
      const anim = new PIXI.AnimatedSprite(textures);
      anim.animationSpeed = animationSpeed;
      anim.loop = true;
      anim.play();

      // Resize or autosize
      anim.width = width || frameWidth;
      anim.height = height || frameHeight;
      anim.anchor.set(0.5);
      anim.x = app.renderer.width / 2;
      anim.y = app.renderer.height / 2;

      app.stage.addChild(anim);
      spriteRef.current = anim;
    });

    return () => {
      // Cleanup on unmount
      if (spriteRef.current) {
        app.stage.removeChild(spriteRef.current);
        spriteRef.current.destroy();
      }
      app.destroy(true, { children: true });
    };
  }, [imageUrl, gridCols, gridRows, animationSpeed, width, height]);

  return <div ref={containerRef} style={{ display: 'inline-block' }} />;
}
