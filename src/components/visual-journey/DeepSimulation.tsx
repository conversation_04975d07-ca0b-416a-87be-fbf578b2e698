import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { safeNavigate } from './VisualJourney';
import { 
  BarChart3,
  Calculator,
  RefreshCw,
  PlayCircle,
  Sliders,
  ChevronDown,
  ChevronUp, 
  ArrowRight,
  Download,
  PlusCircle,
  Activity,
  TrendingUp,
  BarChart,
  AlertTriangle,
  CheckCircle,
  Info,
  Maximize2,
  Pause,
  X
} from 'lucide-react';
import { useGameStore } from '../../store';

// Simulation settings interface
interface SimulationSettings {
  spinCount: number;
  spinSpeed: 'slow' | 'normal' | 'fast' | 'instant';
  visualMode: 'basic' | 'detailed' | 'stats-only';
  targetMetrics: string[];
  stopConditions: {
    enabled: boolean;
    minSpins: number;
    rtpConfidence: number;
    bigWinLimit: number;
  };
  focusAreas: string[];
}

// Simulation result metrics interface
interface SimulationMetrics {
  rtp: number;
  rtpConfidence: number;
  hitRate: number;
  volatility: number;
  featureTriggerRate: number;
  bigWinRate: number;
  maxWin: {
    betMultiplier: number;
    occurred: number;
  };
  symbolDistribution: Record<string, number>;
  payoutDistribution: {
    range: string;
    count: number;
    percentage: number;
  }[];
  spinsCompleted: number;
  simulationTime: number;
  warnings: string[];
  recommendations: string[];
}

// Improved Simulation visualization component
const SimulationVisualizer: React.FC<{
  isRunning: boolean;
  settings: SimulationSettings;
  metrics: SimulationMetrics | null;
  progress: number;
  onStop: () => void;
  onStart:() => void;
  isComplete:boolean;
}> = ({ isRunning, settings, metrics, progress, onStop }) => {
  const [currentView, setCurrentView] = useState<'dashboard' | 'reels' | 'metrics'>('dashboard');
  const [gameRoundNumber, setGameRoundNumber] = useState(1);
  const [currentWin, setCurrentWin] = useState(0);
  
  // Ensure settings is valid with defaults
  const safeSettings = {
    spinCount: settings?.spinCount || 100000,
    spinSpeed: settings?.spinSpeed || 'fast',
    visualMode: settings?.visualMode || 'basic',
    targetMetrics: Array.isArray(settings?.targetMetrics) ? settings.targetMetrics : [],
    stopConditions: {
      enabled: settings?.stopConditions?.enabled || false,
      minSpins: settings?.stopConditions?.minSpins || 10000,
      rtpConfidence: settings?.stopConditions?.rtpConfidence || 95,
      bigWinLimit: settings?.stopConditions?.bigWinLimit || 0
    },
    focusAreas: Array.isArray(settings?.focusAreas) ? settings.focusAreas : []
  };
  
  // Safely ensure metrics has valid data
  const safeMetrics = metrics ? {
    ...metrics,
    rtp: isNaN(metrics.rtp) ? 96 : metrics.rtp,
    rtpConfidence: isNaN(metrics.rtpConfidence) ? 95 : metrics.rtpConfidence,
    hitRate: isNaN(metrics.hitRate) ? 25 : metrics.hitRate,
    volatility: isNaN(metrics.volatility) ? 6 : metrics.volatility,
    featureTriggerRate: isNaN(metrics.featureTriggerRate) ? 0.01 : metrics.featureTriggerRate,
    bigWinRate: isNaN(metrics.bigWinRate) ? 0.005 : metrics.bigWinRate,
    maxWin: {
      betMultiplier: isNaN(metrics.maxWin?.betMultiplier) ? 1000 : metrics.maxWin.betMultiplier,
      occurred: isNaN(metrics.maxWin?.occurred) ? 1 : metrics.maxWin.occurred
    },
    symbolDistribution: metrics.symbolDistribution || {},
    payoutDistribution: metrics.payoutDistribution || [],
    spinsCompleted: isNaN(metrics.spinsCompleted) ? 0 : metrics.spinsCompleted,
    simulationTime: isNaN(metrics.simulationTime) ? 0 : metrics.simulationTime,
    warnings: Array.isArray(metrics.warnings) ? metrics.warnings : [],
    recommendations: Array.isArray(metrics.recommendations) ? metrics.recommendations : []
  } : null;
  
  // Improved reel representation with more realistic symbols
  const reels = [
    ['A', 'K', 'Q', 'J', 'WILD', '10', '9', 'SCATTER'],
    ['K', 'Q', 'J', '10', '9', 'WILD', 'A', 'SCATTER'],
    ['Q', 'J', '10', '9', 'A', 'K', 'WILD', 'SCATTER'],
    ['J', '10', '9', 'A', 'K', 'Q', 'WILD', 'SCATTER'],
    ['10', '9', 'A', 'K', 'Q', 'J', 'WILD', 'SCATTER']
  ];
  
  // Generate random reel positions
  const [reelPositions, setReelPositions] = useState<number[]>([0, 0, 0, 0, 0]);
  
  // Reference for storing reel animation interval ID
  const reelsIntervalRef = useRef<number | null>(null);
  
  // Add event listener for reset
  useEffect(() => {
    const handleReset = () => {
      setGameRoundNumber(1);
      setCurrentWin(0);
    };
    
    // Add event listener to the component's root element
    const root = document.querySelector('.simulation-visualizer');
    if (root) {
      root.addEventListener('reset-gameround', handleReset);
    }
    
    // Clean up on unmount
    return () => {
      if (root) {
        root.removeEventListener('reset-gameround', handleReset);
      }
    };
  }, []);

  // Update reels when running
  useEffect(() => {
    if (!isRunning || settings.spinSpeed === 'instant') {
      // Clear any existing interval if not running or in instant mode
      if (reelsIntervalRef.current !== null) {
        clearInterval(reelsIntervalRef.current);
        reelsIntervalRef.current = null;
      }
      return;
    }
    
    // Clear any existing interval to prevent memory leaks
    if (reelsIntervalRef.current !== null) {
      clearInterval(reelsIntervalRef.current);
    }
    
    // Determine interval time based on spin speed
    const intervalTime = settings.spinSpeed === 'slow' ? 800 : 
                          settings.spinSpeed === 'normal' ? 400 : 100;
    
    // Set up new interval
    reelsIntervalRef.current = window.setInterval(() => {
      try {
        // Update reel positions
        setReelPositions(prevPositions => 
          prevPositions.map(() => Math.floor(Math.random() * 8))
        );
        
        // Increment game round counter
        setGameRoundNumber(prev => prev + 1);
        
        // Generate random win amount for winning spins
        if (Math.random() < 0.3) { // 30% win rate for visualization
          setCurrentWin(parseFloat((Math.random() * 5 + 1).toFixed(2)));
        } else {
          setCurrentWin(0);
        }
      } catch (error) {
        console.error("Error updating reel positions:", error);
        // Clean up on error
        if (reelsIntervalRef.current !== null) {
          clearInterval(reelsIntervalRef.current);
          reelsIntervalRef.current = null;
        }
      }
    }, intervalTime);
    
    // Clean up on dismount or when dependencies change
    return () => {
      if (reelsIntervalRef.current !== null) {
        clearInterval(reelsIntervalRef.current);
        reelsIntervalRef.current = null;
      }
    };
  }, [isRunning, settings.spinSpeed]);
  
  // Win calculation based on currentWin
  const isWin = currentWin > 0;

  // Data for simplified circular progress
  const progressPercentage = progress;
  const dashArray = 283; // Circumference of a circle with r=45: 2 * π * 45
  const dashOffset = dashArray - (dashArray * progressPercentage) / 100;

  // Helper for key metrics rendering
  const renderKeyMetric = (label: string, value: string | number, subValue: string, color: string) => (
    <div className={`bg-${color}-50 rounded-lg p-3 border border-${color}-100 transition-all duration-300 hover:shadow-md`}>
      <div className="text-xs text-gray-500">{label}</div>
      <div className={`text-xl font-bold text-${color}-700`}>{value}</div>
      <div className="text-xs text-gray-500 mt-1">{subValue}</div>
    </div>
  );
  
  // Render dashboard view (combines core metrics with a summary view)
  const renderDashboard = () => (
    <div className="dashboard-view bg-white rounded-lg shadow-sm overflow-hidden">
      {/* Progress indicator and status */}
      <div className="relative bg-gradient-to-r from-blue-900 to-indigo-900 p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-xl font-semibold">Simulation Dashboard</h4>
            <p className="text-blue-200 text-sm mt-1">
              {isRunning 
                ? `Running: ${safeMetrics?.spinsCompleted.toLocaleString() || Math.floor(progress * settings.spinCount / 100).toLocaleString()} spins`
                : metrics ? 'Simulation complete' : 'Ready to start'}
            </p>
          </div>
          
          {/* Circular progress indicator */}
          <div className="relative w-24 h-24 flex items-center justify-center">
            <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
              <circle 
                cx="50" cy="50" r="45" 
                fill="none" 
                stroke="#1e3a8a" 
                strokeWidth="8"
                className="opacity-25"
              />
              <circle 
                cx="50" cy="50" r="45" 
                fill="none" 
                stroke="#60a5fa" 
                strokeWidth="8" 
                strokeDasharray={dashArray} 
                strokeDashoffset={dashOffset}
                strokeLinecap="round"
                className="transition-all duration-500 ease-out"
              />
              <text 
                x="50" y="50" 
                fontSize="20" 
                fontWeight="bold" 
                fill="white" 
                textAnchor="middle" 
                dominantBaseline="middle"
              >
                {progress}%
              </text>
            </svg>
          </div>
        </div>
        
        {/* Controls */}
        <div className="mt-4 flex flex-wrap gap-2">
          {isRunning ? (
            <button
              onClick={onStop}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg flex items-center gap-2 text-sm transition-colors duration-200"
            >
              <Pause className="w-4 h-4" />
              Stop
            </button>
          ) : (
            metrics ? (
              <div className="px-4 py-2 bg-green-600 text-white rounded-lg flex items-center gap-2 text-sm">
                <CheckCircle className="w-4 h-4" />
                Complete
              </div>
            ) : null
          )}
          
          <div className="flex flex-wrap gap-2">
            <button 
              onClick={() => setCurrentView('reels')}
              className="px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white rounded-lg flex items-center gap-1 text-sm transition-colors duration-200"
            >
              <PlayCircle className="w-4 h-4" />
              Reels
            </button>
            <button 
              onClick={() => setCurrentView('metrics')}
              className="px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white rounded-lg flex items-center gap-1 text-sm transition-colors duration-200"
            >
              <BarChart3 className="w-4 h-4" />
              Details
            </button>
          </div>
        </div>
      </div>
      
      {/* Key metrics section */}
      {metrics ? (
        <div className="p-4">
          <div className="grid grid-cols-2 md:grid-cols-2 gap-4">
            {renderKeyMetric(
              "Actual RTP", 
              `${metrics.rtp.toFixed(2)}%`, 
              `±${(100 - metrics.rtpConfidence).toFixed(2)}% confidence`,
              "blue"
            )}
            
            {renderKeyMetric(
              "Hit Rate", 
              `${metrics.hitRate.toFixed(2)}%`, 
              `1 in ${Math.round(100 / metrics.hitRate).toFixed(1)} spins`,
              "green"
            )}
            
            {renderKeyMetric(
              "Feature Triggers", 
              `1:${Math.round(1 / metrics.featureTriggerRate)}`, 
              `${(metrics.featureTriggerRate * 100).toFixed(2)}% chance`,
              "purple"
            )}
            
            {renderKeyMetric(
              "Max Win", 
              `${metrics.maxWin.betMultiplier}x`, 
              `Occurred ${metrics.maxWin.occurred} time${metrics.maxWin.occurred !== 1 ? 's' : ''}`,
              "red"
            )}
          </div>
          
          {/* Compact distribution visual */}
          <div className="mt-6">
            <div className="flex items-center justify-between mb-2">
              <h5 className="font-medium text-gray-700">Win Distribution</h5>
              <div className="text-xs text-gray-500">Based on {metrics.spinsCompleted.toLocaleString()} spins</div>
            </div>
            
            <div className="h-16 flex items-end gap-px rounded overflow-hidden">
              {[
                { range: '0x', percentage: 0.75, color: 'bg-gray-300' },
                { range: '1-2x', percentage: 0.12, color: 'bg-blue-300' },
                { range: '3-5x', percentage: 0.06, color: 'bg-blue-400' },
                { range: '6-10x', percentage: 0.03, color: 'bg-blue-500' },
                { range: '11-50x', percentage: 0.02, color: 'bg-blue-600' },
                { range: '51-100x', percentage: 0.01, color: 'bg-blue-700' },
                { range: '101+x', percentage: 0.01, color: 'bg-blue-800' }
              ].map((dist, idx) => (
                <div key={idx} className="flex-1 group relative" title={`${dist.range}: ${(dist.percentage * 100).toFixed(1)}%`}>
                  <div className={`w-full ${dist.color} h-full transition-all duration-300 group-hover:opacity-80`} 
                       style={{ height: `${Math.max(5, dist.percentage * 100)}%` }}></div>
                  <div className="absolute bottom-0 left-0 right-0 text-center opacity-0 group-hover:opacity-100 transition-opacity text-xs font-medium text-white bg-black/50 py-0.5">
                    {(dist.percentage * 100).toFixed(0)}%
                  </div>
                </div>
              ))}
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>No Win</span>
              <span>Small Wins</span>
              <span>Medium Wins</span>
              <span>Big Wins</span>
            </div>
          </div>
          
          {/* Alerts section */}
          {(metrics.warnings.length > 0 || metrics.recommendations.length > 0) && (
            <div className="mt-6 p-3 bg-gray-50 rounded-lg border border-gray-200">
              <h5 className="font-medium text-gray-700 mb-2">Insights</h5>
              
              <div className="space-y-2 text-sm">
                {metrics.warnings.map((warning, idx) => (
                  <div key={`warning-${idx}`} className="flex items-start gap-2 text-yellow-700">
                    <AlertTriangle className="w-4 h-4 flex-shrink-0 mt-0.5" />
                    <span>{warning}</span>
                  </div>
                ))}
                
                {metrics.recommendations.map((rec, idx) => (
                  <div key={`rec-${idx}`} className="flex items-start gap-2 text-blue-700">
                    <Info className="w-4 h-4 flex-shrink-0 mt-0.5" />
                    <span>{rec}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center p-10 text-center">
          {isRunning ? (
            <div className="animate-pulse">
              <RefreshCw className="w-12 h-12 text-blue-500 mx-auto mb-4 animate-spin" />
              <div className="text-gray-600">Gathering simulation data...</div>
            </div>
          ) : (
            <div>
              <Calculator className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <div className="text-gray-600 mb-2">Run the simulation to see results</div>
              <div className="text-sm text-gray-500">Configure parameters and click "Run Simulation"</div>
            </div>
          )}
        </div>
      )}
    </div>
  );
  
  // Frame animation for lightning bolts
  const [lightningFrame, setLightningFrame] = useState<number>(-1);
  const [winActive, setWinActive] = useState<boolean>(false);
  const [winSize, setWinSize] = useState<'small' | 'medium' | 'big'>('small');
  
  // Win animation effect
  useEffect(() => {
    // Skip animations in stats-only mode
    if (safeSettings.visualMode === 'stats-only') return;
    
    if (isWin) {
      // Determine win size for appropriate effect scaling
      let newWinSize: 'small' | 'medium' | 'big' = 'small';
      if (currentWin > 4) {
        newWinSize = 'big';
      } else if (currentWin > 2) {
        newWinSize = 'medium';
      }
      setWinSize(newWinSize);
      
      // Activate win state
      setWinActive(true);
      
      // Start the lightning animation sequence
      setLightningFrame(0);
      
      // Use requestAnimationFrame for smoother animation
      let frameCount = 0;
      let lastTimestamp = 0;
      let frameRate = 60; // ms between frames (about 16.6 FPS)
      
      // Animation frame function
      const animateFrame = (timestamp: number) => {
        if (!lastTimestamp) lastTimestamp = timestamp;
        
        const elapsed = timestamp - lastTimestamp;
        
        if (elapsed > frameRate) {
          lastTimestamp = timestamp;
          frameCount++;
          
          // Update lightning frame (0-7 for sequence)
          if (frameCount % 2 === 0) { // slow down a bit for better visual
            setLightningFrame(prev => (prev + 1) % 8);
          }
          
          // End animation after 24 frames (about 2-3 seconds)
          if (frameCount >= 24) {
            setLightningFrame(-1);
            setWinActive(false);
            return;
          }
        }
        
        // Continue animation
        animationRef.current = requestAnimationFrame(animateFrame);
      };
      
      // Start animation
      const animationRef: { current: number | null } = { current: null };
      animationRef.current = requestAnimationFrame(animateFrame);
      
      // Clean up
      return () => {
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current);
        }
      };
    } else {
      // Reset when no win
      setLightningFrame(-1);
      setWinActive(false);
    }
  }, [isWin, currentWin, safeSettings.visualMode]);
  
  // Helper for rendering lightning bolts
  const renderLightningEffect = () => {
    if (lightningFrame < 0 || !winActive) return null;
    
    // Different configs based on win size
    const config = {
      small: {
        count: 2,
        positions: [
          { left: '20%', top: '40%' },
          { left: '80%', top: '40%' }
        ],
        size: 80
      },
      medium: {
        count: 4,
        positions: [
          { left: '15%', top: '30%' },
          { left: '85%', top: '30%' },
          { left: '30%', top: '50%' },
          { left: '70%', top: '50%' }
        ],
        size: 100
      },
      big: {
        count: 6,
        positions: [
          { left: '10%', top: '25%' },
          { left: '90%', top: '25%' },
          { left: '30%', top: '40%' },
          { left: '70%', top: '40%' },
          { left: '20%', top: '60%' },
          { left: '80%', top: '60%' }
        ],
        size: 120
      }
    };
    
    // Get the right config based on win size
    const { count, positions, size } = config[winSize];
    
    // Determine which sprite to use based on frame
    // Each bolt has 4 different sprites, cycle through them based on frame
    const boltVariant = (lightningFrame % 4) + 1;
    
    // Render the lightning bolts
    return (
      <>
        {Array.from({ length: count }).map((_, index) => {
          if (index >= positions.length) return null;
          
          const { left, top } = positions[index];
          
          // Alternate sprites between positions for more variety
          const variant = ((index + boltVariant) % 4) + 1;
          
          return (
            <div
              key={`lightning-${index}`}
              className="absolute z-30 pointer-events-none transform -translate-x-1/2 -translate-y-1/2 animate-pulse"
              style={{
                left,
                top,
                width: `${size}px`,
                height: `${size}px`,
                backgroundImage: `url('/assets/effects/lightning_particle${variant}.png')`,
                backgroundSize: 'contain',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center',
                filter: 'drop-shadow(0 0 8px rgba(255, 255, 0, 0.7))',
                opacity: Math.random() * 0.3 + 0.7 // Random slight opacity variation for flicker effect
              }}
            />
          );
        })}
      </>
    );
  };
  
  // Render reels view
  const renderReels = () => (
    <div className="reels-view bg-gray-900 rounded-lg shadow-lg overflow-hidden">
      <div className="p-4 bg-gradient-to-r from-indigo-900 to-blue-900 flex justify-between items-center">
        <h4 className="text-lg font-semibold text-white">Slot Machine Simulation</h4>
        <button
          onClick={() => setCurrentView('dashboard')}
          className="text-white bg-white/10 hover:bg-white/20 rounded-full p-1 transition-colors duration-200"
        >
          <X className="w-5 h-5" />
        </button>
      </div>
      
      {/* Game view area */}
      <div className="p-2 text-center relative overflow-hidden">
        {/* Dynamic background effect */}
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900 to-gray-800 opacity-70"></div>
        
        {/* Lightning effect - positioned lightning bolts */}
        {renderLightningEffect()}
        
        {/* Reel visualization - improved with subtle animations */}
        <div className="relative z-10">
          <div className="flex justify-center gap-1 mb-6">
            {reels.map((reel, reelIndex) => (
              <div key={reelIndex} className="reel bg-gray-800/80 rounded overflow-hidden shadow-lg border border-gray-700">
                {[0, 1, 2].map(pos => {
                  const symbolIndex = (reelPositions[reelIndex] + pos) % reel.length;
                  const symbol = reel[symbolIndex];
                  
                  return (
                    <div 
                      key={pos} 
                      className={`w-10 h-10 flex items-center justify-center text-xs font-semibold 
                                  transition-all duration-300 transform ${isRunning ? 'scale-95' : 'scale-100'}
                                  ${symbol === 'WILD' ? 'bg-gradient-to-br from-yellow-500 to-yellow-700 text-white shadow-inner' : 
                                    symbol === 'SCATTER' ? 'bg-gradient-to-br from-purple-500 to-purple-700 text-white shadow-inner' :
                                    symbol === 'A' || symbol === 'K' ? 'bg-gradient-to-br from-blue-500 to-blue-700 text-white shadow-inner' :
                                    'bg-gradient-to-br from-gray-700 to-gray-800 text-gray-200 shadow-inner'}`}
                    >
                      {symbol}
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
          
          {/* Enhanced win indicator */}
          <div className="h-16 flex items-center justify-center">
            {isWin && (
              <div 
                className={`text-3xl font-bold text-yellow-400 px-8 py-3 rounded-full shadow-lg z-30 relative
                           ${winSize === 'big' ? 'bg-gradient-to-r from-purple-900/80 to-red-900/80 border-2 border-yellow-500' : 
                             winSize === 'medium' ? 'bg-gradient-to-r from-blue-900/80 to-purple-900/80 border border-yellow-400' : 
                             'bg-black/50 border border-yellow-300'}
                           animate-bounce
                          `}
                style={{
                  textShadow: '0 0 10px rgba(255, 215, 0, 0.8)',
                  boxShadow: '0 0 20px rgba(255, 215, 0, 0.4)'
                }}
              >
                WIN ${currentWin.toFixed(2)}!
                
                {/* Add glowing particles for big wins */}
                {winSize !== 'small' && (
                  <div className="absolute inset-0 -z-10 overflow-hidden rounded-full">
                    {Array.from({ length: 8 }).map((_, i) => (
                      <div 
                        key={`glow-${i}`}
                        className="absolute animate-ping rounded-full bg-yellow-300"
                        style={{
                          width: `${Math.random() * 10 + 5}px`,
                          height: `${Math.random() * 10 + 5}px`,
                          top: `${Math.random() * 100}%`,
                          left: `${Math.random() * 100}%`,
                          opacity: Math.random() * 0.5 + 0.2,
                          animationDuration: `${Math.random() * 2 + 1}s`,
                          animationDelay: `${Math.random() * 0.5}s`
                        }}
                      />
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Game and spin information */}
          <div className="mt-4 flex justify-between items-center px-2 py-3 bg-black/20 rounded-lg">
            <div className="text-gray-400 text-sm">
              Game Round #{gameRoundNumber.toLocaleString()}
            </div>
            
            <div className="text-sm">
              {isRunning ? (
                <div className="bg-blue-600 text-white px-3 py-1 rounded-full flex items-center">
                  <div className="mr-2 w-2 h-2 bg-white rounded-full animate-pulse"></div>
                  Spinning...
                </div>
              ) : (
                <div className="bg-gray-700 text-gray-300 px-3 py-1 rounded-full">
                  Paused
                </div>
              )}
            </div>
            
            <div className="text-yellow-400 text-sm font-medium">
              {safeMetrics ? `${safeMetrics.spinsCompleted.toLocaleString()} spins` : 'Ready'}
            </div>
          </div>
        </div>
        
        {/* Stop button - more prominent */}
        {isRunning && (
          <button
            onClick={onStop}
            className="mt-6 px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg flex items-center gap-2 text-base font-medium mx-auto shadow-lg transition-all duration-200 hover:shadow-xl"
          >
            <Pause className="w-5 h-5" />
            Stop Simulation
          </button>
        )}
      </div>
    </div>
  );
  
  // Render detailed metrics view
  const renderDetailedMetrics = () => (
    <div className="metrics-view bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="p-4 bg-gradient-to-r from-blue-900 to-indigo-900 flex justify-between items-center">
        <h4 className="text-lg font-semibold text-white">Detailed Metrics</h4>
        <button
          onClick={() => setCurrentView('dashboard')}
          className="text-white bg-white/10 hover:bg-white/20 rounded-full p-1 transition-colors duration-200"
        >
          <X className="w-5 h-5" />
        </button>
      </div>
      
      <div className="p-4">
        {metrics ? (
          <div className="space-y-6">
            {/* Core metrics - enhanced layout */}
            <div className="grid grid-cols-2 md:grid-cols-2 gap-4">
              <div className="bg-blue-50 rounded-lg p-3 border border-blue-100 shadow-sm">
                <div className="text-xs text-gray-500">Actual RTP</div>
                <div className="text-xl font-bold text-blue-700">{metrics.rtp.toFixed(2)}%</div>
                <div className="text-xs text-gray-500 mt-1">
                  ±{(100 - metrics.rtpConfidence).toFixed(2)}% confidence
                </div>
              </div>
              
              <div className="bg-green-50 rounded-lg p-3 border border-green-100 shadow-sm">
                <div className="text-xs text-gray-500">Hit Rate</div>
                <div className="text-xl font-bold text-green-700">{metrics.hitRate.toFixed(2)}%</div>
                <div className="text-xs text-gray-500 mt-1">
                  1 in {Math.round(100 / metrics.hitRate).toFixed(1)} spins
                </div>
              </div>
              
              <div className="bg-purple-50 rounded-lg p-3 border border-purple-100 shadow-sm">
                <div className="text-xs text-gray-500">Feature Triggers</div>
                <div className="text-xl font-bold text-purple-700">
                  1:{Math.round(1 / metrics.featureTriggerRate)}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {(metrics.featureTriggerRate * 100).toFixed(2)}% chance
                </div>
              </div>
              
              <div className="bg-red-50 rounded-lg p-3 border border-red-100 shadow-sm">
                <div className="text-xs text-gray-500">Max Win</div>
                <div className="text-xl font-bold text-red-700">
                  {metrics.maxWin.betMultiplier}x
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Occurred {metrics.maxWin.occurred} time{metrics.maxWin.occurred !== 1 ? 's' : ''}
                </div>
              </div>
            </div>
            
            {/* Enhanced win distribution with tooltips */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div className="font-medium text-gray-700 mb-4">Win Distribution</div>
              <div className="h-40 flex items-end gap-1">
                {[
                  { range: '0x', percentage: 0.75, color: 'bg-gray-300' },
                  { range: '1-2x', percentage: 0.12, color: 'bg-blue-300' },
                  { range: '3-5x', percentage: 0.06, color: 'bg-blue-400' },
                  { range: '6-10x', percentage: 0.03, color: 'bg-blue-500' },
                  { range: '11-50x', percentage: 0.02, color: 'bg-blue-600' },
                  { range: '51-100x', percentage: 0.01, color: 'bg-blue-700' },
                  { range: '101+x', percentage: 0.01, color: 'bg-blue-800' }
                ].map((dist, idx) => (
                  <div key={idx} className="flex-1 flex flex-col items-center group relative">
                    <div 
                      className={`w-full ${dist.color} rounded-t transition-all duration-300 group-hover:opacity-80`}
                      style={{ height: `${Math.max(8, dist.percentage * 100)}%` }}
                    >
                      <div className="absolute bottom-full left-0 right-0 text-center opacity-0 group-hover:opacity-100 transition-opacity text-xs bg-black text-white py-1 rounded z-10 -mt-0.5">
                        {(dist.percentage * 100).toFixed(1)}%
                      </div>
                    </div>
                    <div className="text-xs text-gray-600 mt-1">{dist.range}</div>
                  </div>
                ))}
              </div>
              
              {/* Enhanced statistics */}
              <div className="mt-4 grid grid-cols-2 gap-3 text-sm">
                <div className="p-2 bg-white rounded border border-gray-200">
                  <div className="text-xs text-gray-500">Most Common Win</div>
                  <div className="font-medium text-gray-800">1-2x bet</div>
                </div>
                <div className="p-2 bg-white rounded border border-gray-200">
                  <div className="text-xs text-gray-500">Avg Win Size</div>
                  <div className="font-medium text-gray-800">4.8x bet</div>
                </div>
                <div className="p-2 bg-white rounded border border-gray-200">
                  <div className="text-xs text-gray-500">RTP Volatility</div>
                  <div className="font-medium text-gray-800">Medium-High</div>
                </div>
              </div>
            </div>
            
            {/* Symbol distribution */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div className="font-medium text-gray-700 mb-3">Symbol Hit Frequency</div>
              <div className="space-y-2">
                {Object.entries(safeMetrics.symbolDistribution).map(([symbol, frequency], idx) => (
                  <div key={idx} className="flex items-center gap-2">
                    <div className="w-16 text-sm">{symbol}</div>
                    <div className="flex-1 bg-gray-200 h-5 rounded-full overflow-hidden">
                      <div 
                        className={`h-full rounded-full ${
                          symbol === 'WILD' ? 'bg-yellow-500' :
                          symbol === 'SCATTER' ? 'bg-purple-500' :
                          symbol.startsWith('H') ? 'bg-blue-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${Math.min(100, frequency * 500)}%` }}
                      ></div>
                    </div>
                    <div className="w-16 text-right text-sm">
                      {(frequency * 100).toFixed(1)}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Warnings and Recommendations */}
            {(metrics.warnings.length > 0 || metrics.recommendations.length > 0) && (
              <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                {metrics.warnings.length > 0 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div className="flex items-center gap-1 text-yellow-800 font-medium mb-3">
                      <AlertTriangle className="w-5 h-5" />
                      <span>Warnings</span>
                    </div>
                    <ul className="text-sm text-yellow-700 space-y-2">
                      {metrics.warnings.map((warning, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <span className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2 flex-shrink-0" />
                          <span>{warning}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {metrics.recommendations.length > 0 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div className="flex items-center gap-1 text-blue-800 font-medium mb-3">
                      <Info className="w-5 h-5" />
                      <span>Recommendations</span>
                    </div>
                    <ul className="text-sm text-blue-700 space-y-2">
                      {metrics.recommendations.map((rec, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                          <span>{rec}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
            
            {/* Simulation info footer */}
            <div className="flex justify-between items-center text-xs text-gray-500 border-t border-gray-200 pt-4">
              <div>
                Simulation completed {metrics.spinsCompleted.toLocaleString()} spins
              </div>
              <div>
                Processed in {metrics.simulationTime} seconds
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            {isRunning ? (
              <div>
                <RefreshCw className="w-12 h-12 text-blue-500 mx-auto mb-4 animate-spin" />
                <div className="text-gray-600">Gathering simulation data...</div>
                <div className="mt-4 w-full bg-gray-200 rounded-full h-2.5">
                  <div 
                    className="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </div>
            ) : (
              <div>
                <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <div className="text-gray-600">Start simulation to view metrics</div>
                <div className="text-sm text-gray-500 mt-2">
                  Configure settings and run the simulation
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
  
  // Main return with view switching
  return (
    <div className="simulation-visualizer">
      {safeSettings.visualMode === 'stats-only' ? (
        // Show only metrics in stats-only mode
        renderDetailedMetrics()
      ) : (
        // Show selected view based on state
        <div className="space-y-6">
          {currentView === 'dashboard' && renderDashboard()}
          {currentView === 'reels' && renderReels()}
          {currentView === 'metrics' && renderDetailedMetrics()}
          
          {/* Removed Lightning Animation Test Area */}
        </div>
      )}
    </div>
  );
};

// Simulation Settings Component
const SimulationSettings: React.FC<{
  settings: SimulationSettings;
  onSettingsChange: (settings: SimulationSettings) => void;
}> = ({ settings, onSettingsChange }) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  // Available metrics to track
  const availableMetrics = [
    { id: 'rtp', name: 'RTP' },
    { id: 'hit-rate', name: 'Hit Rate' },
    { id: 'volatility', name: 'Volatility' },
    { id: 'feature-triggers', name: 'Feature Triggers' },
    { id: 'symbol-distribution', name: 'Symbol Distribution' },
    { id: 'win-distribution', name: 'Win Distribution' }
  ];
  
  // Focus areas
  const focusAreas = [
    { id: 'base-game', name: 'Base Game' },
    { id: 'bonus-features', name: 'Bonus Features' },
    { id: 'big-wins', name: 'Big Wins' },
    { id: 'symbol-balance', name: 'Symbol Balance' }
  ];
  
  // Ensure settings is valid with defaults if needed
  const safeSettings = {
    spinCount: settings?.spinCount || 100000,
    spinSpeed: settings?.spinSpeed || 'fast',
    visualMode: settings?.visualMode || 'basic',
    targetMetrics: Array.isArray(settings?.targetMetrics) ? settings.targetMetrics : [],
    stopConditions: {
      enabled: settings?.stopConditions?.enabled || false,
      minSpins: settings?.stopConditions?.minSpins || 10000,
      rtpConfidence: settings?.stopConditions?.rtpConfidence || 95,
      bigWinLimit: settings?.stopConditions?.bigWinLimit || 0
    },
    focusAreas: Array.isArray(settings?.focusAreas) ? settings.focusAreas : []
  };

  // Toggle a metric
  const toggleMetric = (metricId: string) => {
    try {
      if (!metricId) return;
      
      if (safeSettings.targetMetrics.includes(metricId)) {
        onSettingsChange({
          ...safeSettings,
          targetMetrics: safeSettings.targetMetrics.filter(id => id !== metricId)
        });
      } else {
        onSettingsChange({
          ...safeSettings,
          targetMetrics: [...safeSettings.targetMetrics, metricId]
        });
      }
    } catch (error) {
      console.error("Error toggling metric:", error);
    }
  };
  
  // Toggle a focus area
  const toggleFocusArea = (areaId: string) => {
    try {
      if (!areaId) return;
      
      if (safeSettings.focusAreas.includes(areaId)) {
        onSettingsChange({
          ...safeSettings,
          focusAreas: safeSettings.focusAreas.filter(id => id !== areaId)
        });
      } else {
        onSettingsChange({
          ...safeSettings,
          focusAreas: [...safeSettings.focusAreas, areaId]
        });
      }
    } catch (error) {
      console.error("Error toggling focus area:", error);
    }
  };
  
  return (
    <div className="simulation-settings bg-white border border-gray-200 rounded-lg overflow-hidden">
      <div className="p-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
        <div className="font-medium text-gray-800">Simulation Settings</div>
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="text-sm text-blue-600 flex items-center gap-1"
        >
          {showAdvanced ? (
            <>
              <ChevronUp className="w-4 h-4" />
              Hide Advanced
            </>
          ) : (
            <>
              <ChevronDown className="w-4 h-4" />
              Show Advanced
            </>
          )}
        </button>
      </div>
      
      <div className="p-2">
        <div className="grid grid-cols-1 md:grid-cols-1 gap-2">
          {/* Basic Settings */}
          <div>
            <div className="mb-2 p-2 bg-gray-50 border rounded">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Simulation Size
              </label>
              <select
                value={safeSettings.spinCount}
                onChange={(e) => onSettingsChange({
                  ...safeSettings,
                  spinCount: parseInt(e.target.value) || 100000
                })}
                className="w-full p-2 border border-gray-300 rounded-lg"
              >
                <option value={10000}>Quick (10K spins)</option>
                <option value={100000}>Standard (100K spins)</option>
                <option value={1000000}>Detailed (1M spins)</option>
                <option value={10000000}>Comprehensive (10M spins)</option>
              </select>
            </div>
            
            <div className="mb-2 p-2 bg-gray-50 border rounded">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Visualization Mode
              </label>
              <select
                value={safeSettings.visualMode}
                onChange={(e) => onSettingsChange({
                  ...safeSettings,
                  visualMode: e.target.value as 'basic' | 'detailed' | 'stats-only'
                })}
                className="w-full p-2 border border-gray-300 rounded-lg"
              >
                <option value="basic">Basic (Show reels)</option>
                <option value="detailed">Detailed (Show reels + stats)</option>
                <option value="stats-only">Stats Only (Faster)</option>
              </select>
            </div>
            
            <div className=" p-2 bg-gray-50 border rounded">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Simulation Speed
              </label>
              <select
                value={safeSettings.spinSpeed}
                onChange={(e) => onSettingsChange({
                  ...safeSettings,
                  spinSpeed: e.target.value as 'slow' | 'normal' | 'fast' | 'instant'
                })}
                className="w-full p-2 border border-gray-300 rounded-lg"
              >
                <option value="slow">Slow (800ms)</option>
                <option value="normal">Normal (400ms)</option>
                <option value="fast">Fast (100ms)</option>
                <option value="instant">Instant (No animation)</option>
              </select>
            </div>
          </div>
          
          {/* Target Metrics */}
          <div className='bg-gray-50 border p-2 rounded-md'>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Metrics to Track
            </label>
            <div className="grid grid-cols-2 gap-2">
              {availableMetrics.map(metric => (
                <div key={metric.id} className="flex items-center bg-white border p-1 rounded-md">
                  <input
                    type="checkbox"
                    id={`metric-${metric.id}`}
                    checked={safeSettings.targetMetrics.includes(metric.id)}
                    onChange={() => toggleMetric(metric.id)}
                    className="mr-2"
                  />
                  <label 
                    htmlFor={`metric-${metric.id}`}
                    className="text-[12px] text-gray-700 cursor-pointer"
                  >
                    {metric.name}
                  </label>
                </div>
              ))}
            </div>
            </div>
          <div className='bg-gray-50 border p-2 rounded-md'>

            <div className="">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Focus Areas
              </label>
              <div className="grid grid-cols-2 gap-2">
                {focusAreas.map(area => (
                  <div key={area.id} className="flex items-center bg-white border p-1 rounded-md">
                    <input
                      type="checkbox"
                      id={`area-${area.id}`}
                      checked={safeSettings.focusAreas.includes(area.id)}
                      onChange={() => toggleFocusArea(area.id)}
                      className="mr-2"
                    />
                    <label 
                      htmlFor={`area-${area.id}`}
                      className="text-[12px] text-gray-700 cursor-pointer"
                    >
                      {area.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        
        {/* Advanced Settings */}
        {showAdvanced && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h3 className="text-sm font-medium text-gray-700 mb-4">Advanced Settings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
              <div>
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <input
                      type="checkbox"
                      id="stop-conditions"
                      checked={safeSettings.stopConditions.enabled}
                      onChange={(e) => onSettingsChange({
                        ...safeSettings,
                        stopConditions: {
                          ...safeSettings.stopConditions,
                          enabled: e.target.checked
                        }
                      })}
                      className="mr-2"
                    />
                    <label 
                      htmlFor="stop-conditions"
                      className="text-sm font-medium text-gray-700"
                    >
                      Enable Auto-Stop Conditions
                    </label>
                  </div>
                  
                  <div className={safeSettings.stopConditions.enabled ? '' : 'opacity-50 pointer-events-none'}>
                    <div className="mb-3">
                      <label className="block text-xs text-gray-600 mb-1">
                        Minimum Spins
                      </label>
                      <input
                        type="number"
                        min="1000"
                        max="1000000"
                        value={safeSettings.stopConditions.minSpins}
                        onChange={(e) => onSettingsChange({
                          ...safeSettings,
                          stopConditions: {
                            ...safeSettings.stopConditions,
                            minSpins: parseInt(e.target.value) || 10000
                          }
                        })}
                        className="w-full p-1.5 border border-gray-300 rounded-lg"
                        disabled={!safeSettings.stopConditions.enabled}
                      />
                    </div>
                    
                    <div className="mb-3">
                      <label className="block text-xs text-gray-600 mb-1">
                        RTP Confidence (%)
                      </label>
                      <input
                        type="number"
                        min="90"
                        max="99.9"
                        step="0.1"
                        value={safeSettings.stopConditions.rtpConfidence}
                        onChange={(e) => onSettingsChange({
                          ...safeSettings,
                          stopConditions: {
                            ...safeSettings.stopConditions,
                            rtpConfidence: parseFloat(e.target.value) || 95
                          }
                        })}
                        className="w-full p-1.5 border border-gray-300 rounded-lg"
                        disabled={!safeSettings.stopConditions.enabled}
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Detailed Analysis Options
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="analysis-rtp-components"
                        className="mr-2"
                      />
                      <label 
                        htmlFor="analysis-rtp-components"
                        className="text-sm text-gray-700"
                      >
                        RTP Component Breakdown
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="analysis-feature-details"
                        className="mr-2"
                      />
                      <label 
                        htmlFor="analysis-feature-details"
                        className="text-sm text-gray-700"
                      >
                        Detailed Feature Analysis
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="analysis-bankroll-simulation"
                        className="mr-2"
                      />
                      <label 
                        htmlFor="analysis-bankroll-simulation"
                        className="text-sm text-gray-700"
                      >
                        Bankroll Simulation
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="analysis-statistical-anomalies"
                        className="mr-2"
                      />
                      <label 
                        htmlFor="analysis-statistical-anomalies"
                        className="text-sm text-gray-700"
                      >
                        Detect Statistical Anomalies
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Simulation Results Component
const SimulationResults: React.FC<{
  metrics: SimulationMetrics | null;
  isComplete: boolean;
  applySimulationResults: () => void;
  exportReport: () => void;
}> = ({ metrics, isComplete, applySimulationResults, exportReport }) => {
  const [activeTab, setActiveTab] = useState('summary');
  
  // Safely ensure metrics has valid data to prevent rendering errors
  const safeMetrics = metrics ? {
    ...metrics,
    rtp: isNaN(metrics.rtp) ? 96 : metrics.rtp,
    rtpConfidence: isNaN(metrics.rtpConfidence) ? 95 : metrics.rtpConfidence,
    hitRate: isNaN(metrics.hitRate) ? 25 : metrics.hitRate,
    volatility: isNaN(metrics.volatility) ? 6 : metrics.volatility,
    featureTriggerRate: isNaN(metrics.featureTriggerRate) ? 0.01 : metrics.featureTriggerRate,
    bigWinRate: isNaN(metrics.bigWinRate) ? 0.005 : metrics.bigWinRate,
    maxWin: {
      betMultiplier: isNaN(metrics.maxWin?.betMultiplier) ? 1000 : metrics.maxWin.betMultiplier,
      occurred: isNaN(metrics.maxWin?.occurred) ? 1 : metrics.maxWin.occurred
    },
    symbolDistribution: metrics.symbolDistribution || {},
    payoutDistribution: metrics.payoutDistribution || [],
    spinsCompleted: isNaN(metrics.spinsCompleted) ? 0 : metrics.spinsCompleted,
    simulationTime: isNaN(metrics.simulationTime) ? 0 : metrics.simulationTime,
    warnings: Array.isArray(metrics.warnings) ? metrics.warnings : [],
    recommendations: Array.isArray(metrics.recommendations) ? metrics.recommendations : []
  } : null;
  
  if (!safeMetrics || !isComplete) {
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-3" />
        <h4 className="text-lg font-medium text-gray-700 mb-2">No Simulation Results</h4>
        <p className="text-sm text-gray-500">
          Run a complete simulation to see detailed analysis
        </p>
      </div>
    );
  }
  
  return (
    <div className="simulation-results bg-white border border-gray-200 rounded-lg overflow-hidden">
      <div className="bg-blue-50 p-4 border-b border-blue-200">
        <h3 className="text-lg font-bold text-blue-800">Simulation Complete</h3>
        <p className="text-sm text-blue-700">
          {safeMetrics.spinsCompleted.toLocaleString()} spins analyzed in {safeMetrics.simulationTime} seconds
        </p>
      </div>
      
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 bg-white">
        <div className="flex">
          <button
            className={`px-4 py-3 text-sm font-medium ${
              activeTab === 'summary' 
                ? 'text-blue-600 border-b-2 border-blue-600' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('summary')}
          >
            Summary
          </button>
          <button
            className={`px-4 py-3 text-sm font-medium ${
              activeTab === 'rtp' 
                ? 'text-blue-600 border-b-2 border-blue-600' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('rtp')}
          >
            RTP Analysis
          </button>
          <button
            className={`px-4 py-3 text-sm font-medium ${
              activeTab === 'distribution' 
                ? 'text-blue-600 border-b-2 border-blue-600' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('distribution')}
          >
            Distributions
          </button>
          <button
            className={`px-4 py-3 text-sm font-medium ${
              activeTab === 'recommendations' 
                ? 'text-blue-600 border-b-2 border-blue-600' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('recommendations')}
          >
            Recommendations
          </button>
        </div>
      </div>
      
      {/* Tab Content */}
      <div className="p-4">
        {activeTab === 'summary' && (
          <div className="summary-tab">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="text-xs text-gray-500">Actual RTP</div>
                <div className="text-xl font-bold text-blue-700">{safeMetrics.rtp.toFixed(2)}%</div>
                <div className="text-xs text-gray-500 mt-1">
                  ±{(100 - safeMetrics.rtpConfidence).toFixed(2)}% confidence
                </div>
              </div>
              
              <div className="bg-green-50 rounded-lg p-3">
                <div className="text-xs text-gray-500">Hit Rate</div>
                <div className="text-xl font-bold text-green-700">{safeMetrics.hitRate.toFixed(2)}%</div>
                <div className="text-xs text-gray-500 mt-1">
                  1 in {Math.round(100 / Math.max(0.1, safeMetrics.hitRate)).toFixed(1)} spins
                </div>
              </div>
              
              <div className="bg-purple-50 rounded-lg p-3">
                <div className="text-xs text-gray-500">Feature Triggers</div>
                <div className="text-xl font-bold text-purple-700">
                  1:{Math.round(1 / Math.max(0.001, safeMetrics.featureTriggerRate))}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {(safeMetrics.featureTriggerRate * 100).toFixed(2)}% chance
                </div>
              </div>
              
              <div className="bg-red-50 rounded-lg p-3">
                <div className="text-xs text-gray-500">Max Win</div>
                <div className="text-xl font-bold text-red-700">
                  {safeMetrics.maxWin.betMultiplier}x
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Occurred {safeMetrics.maxWin.occurred} time{safeMetrics.maxWin.occurred !== 1 ? 's' : ''}
                </div>
              </div>
            </div>
            
            {/* Warnings and Recommendations */}
            {(safeMetrics.warnings.length > 0 || safeMetrics.recommendations.length > 0) && (
              <div className="space-y-4">
                {safeMetrics.warnings.length > 0 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <div className="flex items-center gap-1 text-yellow-800 font-medium mb-2">
                      <AlertTriangle className="w-4 h-4" />
                      <span>Warnings</span>
                    </div>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      {safeMetrics.warnings.map((warning, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <span className="w-1 h-1 bg-yellow-500 rounded-full mt-2 flex-shrink-0" />
                          <span>{warning}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {safeMetrics.recommendations.length > 0 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-center gap-1 text-blue-800 font-medium mb-2">
                      <Info className="w-4 h-4" />
                      <span>Recommendations</span>
                    </div>
                    <ul className="text-sm text-blue-700 space-y-1">
                      {safeMetrics.recommendations.map((rec, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <span className="w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                          <span>{rec}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
            
            <div className="mt-6 flex justify-end space-x-3 items-center">
              <button 
                className="px-4 py-2 bg-green-600 text-white rounded-lg flex items-center gap-2 text-sm hover:bg-green-700"
                onClick={applySimulationResults}
              >
                <CheckCircle className="w-4 h-4" />
                Apply to Configuration
              </button>
              
              <div className="flex gap-2">
                <button 
                  onClick={() => exportReport('json')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg flex items-center gap-2 text-sm hover:bg-blue-700"
                >
                  <Download className="w-4 h-4" />
                  Export JSON
                </button>
                
                <button 
                  onClick={() => exportReport('csv')}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg flex items-center gap-2 text-sm hover:bg-purple-700"
                >
                  <Download className="w-4 h-4" />
                  Export CSV
                </button>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'rtp' && (
          <div className="rtp-tab">
            <div className="bg-white p-4 rounded-lg border border-gray-200 mb-4">
              <h4 className="font-medium text-gray-800 mb-3">RTP Components</h4>
              
              <div className="relative h-40 mb-4 bg-gray-50 p-3 rounded-md pt-4 p-4 border">
                {/* Simple RTP component chart */}
                <div className="absolute inset-x-0 bottom-0 h-32 flex items-end">
                  <div className="flex-1 h-full flex flex-col justify-end">
                    <div 
                      className="w-full bg-blue-500"
                      style={{ height: '70%' }}
                    >
                      <div className="text-center text-white font-medium pt-2">Base</div>
                    </div>
                  </div>
                  <div className="flex-1 h-full flex flex-col justify-end">
                    <div 
                      className="w-full bg-green-500"
                      style={{ height: '20%' }}
                    >
                      <div className="text-center text-white font-medium pt-2">Bonus</div>
                    </div>
                  </div>
                  <div className="flex-1 h-full flex flex-col justify-end">
                    <div 
                      className="w-full bg-purple-500"
                      style={{ height: '10%' }}
                    >
                      <div className="text-center text-white font-medium pt-2">Jackpot</div>
                    </div>
                  </div>
                  <div className="flex-1 h-full flex flex-col justify-end">
                    <div 
                      className="w-full bg-yellow-500"
                      style={{ height: '96%' }}
                    >
                      <div className="text-center text-white font-medium pt-2">Total</div>
                    </div>
                  </div>
                </div>
                
                {/* RTP values */}
                <div className="absolute inset-x-0 top-0">
                  <div className="flex justify-around text-sm">
                    <div className="text-center">
                      <div className="font-medium text-blue-700">67.2%</div>
                      <div className="text-xs text-gray-500">Base Game</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-green-700">19.2%</div>
                      <div className="text-xs text-gray-500">Bonus Features</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-purple-700">9.6%</div>
                      <div className="text-xs text-gray-500">Jackpots</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-yellow-700">{safeMetrics.rtp.toFixed(2)}%</div>
                      <div className="text-xs text-gray-500">Total RTP</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="p-2 bg-gray-50 border rounded">
                  <div className="text-xs text-gray-500">Target RTP</div>
                  <div className="font-medium text-gray-800">96.00%</div>
                </div>
                <div className="p-2 bg-gray-50 border rounded">
                  <div className="text-xs text-gray-500">Deviation</div>
                  <div className="font-medium text-gray-800">
                    {(safeMetrics.rtp - 96).toFixed(2)}%
                  </div>
                </div>
                <div className="p-2 bg-gray-50 border rounded">
                  <div className="text-xs text-gray-500">Confidence</div>
                  <div className="font-medium text-gray-800">
                    {safeMetrics.rtpConfidence.toFixed(2)}%
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <h4 className="font-medium text-gray-800 mb-3">RTP Variance Over Time</h4>
              
              {/* Simplified RTP variance chart */}
              <div className="h-40 bg-gray-50 p-2 rounded border border-gray-200 relative mb-3">
                <div className="absolute left-0 right-0 top-1/2 border-b border-gray-300 border-dashed"></div>
                <div className="h-full flex items-center">
                  <div className="w-full h-16 relative">
                    {/* Simulated RTP line */}
                    <svg className="w-full h-full overflow-hidden">
                      <path 
                        d="M0,30 C20,40 40,20 60,25 C80,30 100,35 120,30 C140,25 160,20 180,15 C200,10 220,20 240,15 C260,10 280,5 300,10 C320,15 340,20 360,15 C380,10 400,15 420,20 C440,25 460,20 480,15 C500,10 520,10 540,15 C560,20 580,25 600,30" 
                        fill="none" 
                        stroke="#3b82f6" 
                        strokeWidth="2"
                      />
                    </svg>
                    
                    {/* Target RTP line */}
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t-2 border-red-400"></div>
                    </div>
                  </div>
                </div>
                
                {/* Labels */}
                <div className="absolute left-2 top-2 text-xs text-gray-500">RTP %</div>
                <div className="absolute right-2 bottom-2 text-xs text-gray-500">Spins</div>
                <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-xs text-red-500 font-medium">
                  Target: 96%
                </div>
              </div>
              
              <p className="text-sm text-gray-600">
                RTP stabilizes after approximately {Math.max(1, Math.floor(safeMetrics.spinsCompleted * 0.4)).toLocaleString()} spins, with remaining variance within ±{(100 - safeMetrics.rtpConfidence).toFixed(2)}%.
              </p>
            </div>
          </div>
        )}
        
        {activeTab === 'distribution' && (
          <div className="distribution-tab">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <h4 className="font-medium text-gray-800 mb-3">Win Distribution</h4>
                
                <div className="h-48 mb-3">
                  {/* Win distribution chart - always populated */}
                  <div className="h-40 flex items-end gap-1 border border-gray-200 rounded p-2 bg-gray-50">
                    {/* Always use sample distribution data */}
                    {[
                      { range: '0x', percentage: 0.75 },
                      { range: '1-2x', percentage: 0.12 },
                      { range: '3-5x', percentage: 0.06 },
                      { range: '6-10x', percentage: 0.03 },
                      { range: '11-50x', percentage: 0.02 },
                      { range: '51-100x', percentage: 0.01 },
                      { range: '101+x', percentage: 0.01 }
                    ].map((dist, idx) => (
                      <div key={idx} className="flex-1 flex flex-col items-center">
                        <div 
                          className="w-full bg-blue-500 rounded-t"
                          style={{ 
                            height: `${Math.max(8, dist.percentage * 100)}%`,
                            opacity: 0.6 + (idx * 0.05)
                          }}
                        />
                        <div className="text-xs text-gray-600 mt-1">{dist.range}</div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="p-2 bg-gray-50 rounded">
                    <div className="text-xs text-gray-500">Most Common Win</div>
                    <div className="font-medium text-gray-800">1-2x bet</div>
                  </div>
                  <div className="p-2 bg-gray-50 rounded">
                    <div className="text-xs text-gray-500">Avg Win</div>
                    <div className="font-medium text-gray-800">4.8x bet</div>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <h4 className="font-medium text-gray-800 mb-3">Symbol Hit Frequency</h4>
                
                <div className="h-48 mb-3 ">
                  {/* Symbol hit frequency chart - simplified horizontal bars */}
                  <div className="space-y-2 p-3 bg-gray-50 border dounded-lg">
                    {Object.entries(safeMetrics.symbolDistribution).map(([symbol, frequency], idx) => (
                      <div key={symbol} className="flex items-center gap-2">
                        <div className="w-16 text-sm">{symbol}</div>
                        <div className="flex-1 bg-gray-100 h-4 rounded overflow-hidden">
                          <div 
                            className="h-full rounded"
                            style={{
                              width: `${Math.min(100, frequency * 5)}%`,
                              backgroundColor: symbol === 'WILD' 
                                ? '#f59e0b' 
                                : symbol === 'SCATTER'
                                  ? '#8b5cf6'
                                  : `hsl(${210 - idx * 30}, 80%, 60%)`
                            }}
                          />
                        </div>
                        <div className="w-12 text-sm text-right">{(frequency * 100).toFixed(1)}%</div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="text-sm text-gray-600 mt-7">
                  Symbol distribution is {safeMetrics.warnings.length > 0 ? 'suboptimal' : 'well-balanced'} with {Object.keys(safeMetrics.symbolDistribution).length} unique symbols.
                </div>
              </div>
            </div>
            
            <div className="mt-6 bg-white p-4 rounded-lg border border-gray-200">
              <h4 className="font-medium text-gray-800 mb-3">Feature Trigger Analysis</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="p-3 bg-gray-50 rounded">
                  <div className="text-xs text-gray-500">Trigger Frequency</div>
                  <div className="font-medium text-gray-800">
                    1 in {Math.round(1 / Math.max(0.001, safeMetrics.featureTriggerRate))} spins
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {(safeMetrics.featureTriggerRate * 100).toFixed(2)}% chance per spin
                  </div>
                </div>
                
                <div className="p-3 bg-gray-50 rounded">
                  <div className="text-xs text-gray-500">Feature RTP Contribution</div>
                  <div className="font-medium text-gray-800">
                    19.2% (20% of total)
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Average feature value: 36.6x bet
                  </div>
                </div>
                
                <div className="p-3 bg-gray-50 rounded">
                  <div className="text-xs text-gray-500">Feature Hit Distribution</div>
                  <div className="font-medium text-gray-800">
                    Well-distributed
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Max gap: 214 spins, Avg gap: 52 spins
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'recommendations' && (
          <div className="recommendations-tab">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-2 text-green-800 font-medium mb-4">
                  <CheckCircle className="w-5 h-5" />
                  <span>Strengths</span>
                </div>
                
                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                      <CheckCircle className="w-3 h-3 text-green-600" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-green-800">Balanced RTP Distribution</div>
                      <div className="text-xs text-green-700">
                        Good balance between base game (70%) and features (30%)
                      </div>
                    </div>
                  </li>
                  
                  <li className="flex items-start gap-2">
                    <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                      <CheckCircle className="w-3 h-3 text-green-600" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-green-800">Hit Frequency</div>
                      <div className="text-xs text-green-700">
                        {safeMetrics.hitRate.toFixed(1)}% hit rate ensures good player engagement
                      </div>
                    </div>
                  </li>
                  
                  <li className="flex items-start gap-2">
                    <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                      <CheckCircle className="w-3 h-3 text-green-600" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-green-800">Feature Pacing</div>
                      <div className="text-xs text-green-700">
                        Feature trigger rate of 1:{Math.round(1 / Math.max(0.001, safeMetrics.featureTriggerRate))} is optimal for player retention
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
              
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center gap-2 text-yellow-800 font-medium mb-4">
                  <AlertTriangle className="w-5 h-5" />
                  <span>Areas for Improvement</span>
                </div>
                
                <ul className="space-y-3">
                  {safeMetrics.warnings.map((warning, idx) => (
                    <li key={idx} className="flex items-start gap-2">
                      <div className="w-5 h-5 rounded-full bg-yellow-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <AlertTriangle className="w-3 h-3 text-yellow-600" />
                      </div>
                      <div className="text-sm text-yellow-800">{warning}</div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            
            <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-blue-800 font-medium mb-4">
                <Info className="w-5 h-5" />
                <span>Optimization Opportunities</span>
              </div>
              
              <div className="space-y-3">
                {safeMetrics.recommendations.map((rec, idx) => (
                  <div key={idx} className="flex items-start gap-3">
                    <div 
                      className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 font-medium text-blue-700"
                    >
                      {idx + 1}
                    </div>
                    <div>
                      <div className="text-sm font-medium text-blue-800">{rec}</div>
                      <div className="text-xs text-blue-700 mt-0.5">
                        {idx === 0 ? 'High impact - strongly recommended' :
                         idx === 1 ? 'Medium impact - consider implementing' :
                         'Lower impact - optional enhancement'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 flex justify-end space-x-3">
                <button 
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm flex items-center gap-2"
                  onClick={applySimulationResults}
                >
                  <PlusCircle className="w-4 h-4" />
                  Apply Recommendations
                </button>
                
                <div className="flex gap-2">
                  <button 
                    onClick={() => exportReport('json')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg flex items-center gap-2 text-sm hover:bg-blue-700"
                  >
                    <Download className="w-4 h-4" />
                    Export JSON
                  </button>
                  
                  <button 
                    onClick={() => exportReport('csv')}
                    className="px-4 py-2 bg-purple-600 text-white rounded-lg flex items-center gap-2 text-sm hover:bg-purple-700"
                  >
                    <Download className="w-4 h-4" />
                    Export CSV
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Error Boundary Component
class SimulationErrorBoundary extends React.Component<
  { children: React.ReactNode }, 
  { hasError: boolean, errorMessage: string }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, errorMessage: '' };
  }

  static getDerivedStateFromError(error: Error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, errorMessage: error.message };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error to console
    console.error("Simulation error caught by boundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI when an error occurs
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <h3 className="text-xl font-bold text-red-700 mb-3">Simulation Error</h3>
          <p className="text-red-600 mb-4">An error occurred while running the simulation.</p>
          <p className="text-sm text-gray-700 mb-4">{this.state.errorMessage}</p>
          <button
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            onClick={() => this.setState({ hasError: false, errorMessage: '' })}
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Main Deep Simulation Component
const DeepSimulation: React.FC = () => {
  const { config, updateConfig, nextStep } = useGameStore();
  
  // State for simulation settings
  const [settings, setSettings] = useState<SimulationSettings>({
    spinCount: 100000,
    spinSpeed: 'fast',
    visualMode: 'basic',
    targetMetrics: ['rtp', 'hit-rate', 'volatility', 'feature-triggers'],
    stopConditions: {
      enabled: false,
      minSpins: 10000,
      rtpConfidence: 95,
      bigWinLimit: 0
    },
    focusAreas: ['base-game', 'bonus-features']
  });
  
  // State for simulation status
  const [isRunning, setIsRunning] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [progress, setProgress] = useState(0);
  const [metrics, setMetrics] = useState<SimulationMetrics | null>(null);
  
  // Reset simulation
  const resetSimulation = () => {
    setIsRunning(false);
    setIsComplete(false);
    setProgress(0);
    setMetrics(null);
    
    // Reset the game round counter in the visualizer
    try {
      const visualizerElement = document.querySelector('.simulation-visualizer');
      if (visualizerElement) {
        // Use a custom event to signal the visualizer to reset
        const resetEvent = new CustomEvent('reset-gameround');
        visualizerElement.dispatchEvent(resetEvent);
      }
    } catch (error) {
      console.error("Error resetting game round:", error);
    }
  };
  
  // Reference for storing interval ID
  const progressIntervalRef = useRef<number | null>(null);
  
  // Start simulation with improved error handling
  const startSimulation = () => {
    try {
      resetSimulation();
      setIsRunning(true);
      
      // Use a smaller number of spins for better performance
      const adjustedSpinCount = settings.spinSpeed === 'instant' 
        ? settings.spinCount 
        : Math.min(settings.spinCount, 2000000); // Cap at 2M spins for better performance
      
      // Simulate spin progress with intervals
      let spinsCompleted = 0;
      const totalSpins = adjustedSpinCount;
      // Longer interval for better UI responsiveness
      const updateInterval = settings.spinSpeed === 'instant' ? 80 : 250;
      
      // Clear any existing interval to prevent memory leaks
      if (progressIntervalRef.current !== null) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
      
      // Use requestAnimationFrame for smoother UI updates
      window.requestAnimationFrame(() => {
        // Store the interval ID in the ref with a slight delay to allow UI to update
        setTimeout(() => {
          try {
            progressIntervalRef.current = window.setInterval(() => {
              try {
                // Calculate spins to add this interval (smaller batches for better UI responsiveness)
                const spinsToAdd = settings.spinSpeed === 'instant' 
                  ? Math.floor(totalSpins * 0.03) // Smaller batches
                  : settings.spinSpeed === 'fast'
                    ? Math.floor(totalSpins * 0.015)
                    : Math.floor(totalSpins * 0.008);
                
                spinsCompleted = Math.min(totalSpins, spinsCompleted + spinsToAdd);
                const newProgress = Math.floor((spinsCompleted / totalSpins) * 100);
                
                // Update UI in smaller batches only when progress changes
                if (newProgress > progress) {
                  setProgress(newProgress);
                  
                  // Update metrics less frequently to reduce CPU load
                  if (newProgress % 5 === 0 || newProgress >= 100) {
                    // Use requestAnimationFrame for smoother updates
                    window.requestAnimationFrame(() => {
                      try {
                        updateSimulationMetrics(spinsCompleted, totalSpins);
                      } catch (metricsError) {
                        console.error("Error updating metrics:", metricsError);
                      }
                    });
                  }
                }
                
                // Stop when complete
                if (spinsCompleted >= totalSpins) {
                  if (progressIntervalRef.current !== null) {
                    clearInterval(progressIntervalRef.current);
                    progressIntervalRef.current = null;
                  }
                  
                  // Use requestAnimationFrame and setTimeout for smoother UI updates
                  window.requestAnimationFrame(() => {
                    setTimeout(() => {
                      try {
                        finalizeSimulation(totalSpins);
                      } catch (finalizeError) {
                        console.error("Error finalizing simulation:", finalizeError);
                        setIsRunning(false);
                      }
                    }, 100);
                  });
                }
              } catch (intervalError) {
                console.error("Error in simulation interval:", intervalError);
                // Clean up on error
                if (progressIntervalRef.current !== null) {
                  clearInterval(progressIntervalRef.current);
                  progressIntervalRef.current = null;
                }
                setIsRunning(false);
              }
            }, updateInterval);
          } catch (setupError) {
            console.error("Error setting up simulation:", setupError);
            setIsRunning(false);
          }
        }, 50);
      });
    } catch (error) {
      console.error("Critical error starting simulation:", error);
      setIsRunning(false);
      alert("Failed to start simulation. Please try again with different settings.");
    }
  };
  
  // Stop simulation
  const stopSimulation = () => {
    setIsRunning(false);
    
    // Clear the interval
    if (progressIntervalRef.current !== null) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
    
    // If progress is over 30%, consider simulation valid but incomplete
    if (progress > 30) {
      const effectiveSpins = Math.floor((settings.spinCount * progress) / 100);
      finalizeSimulation(effectiveSpins);
    }
  };
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (progressIntervalRef.current !== null) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
    };
  }, []);
  
  // Update metrics during simulation
  const updateSimulationMetrics = (spinsCompleted: number, totalSpins: number) => {
    try {
      // Prevent division by zero or negative values
      if (totalSpins <= 0 || spinsCompleted < 0) {
        console.warn("Invalid spin values:", { spinsCompleted, totalSpins });
        return;
      }
      
      // Base RTP on a curve that starts at random and converges to 96%
      const completionRatio = Math.min(1, Math.max(0, spinsCompleted / totalSpins));
      const randomFactor = 1 - completionRatio;
      
      // Stabilize calculations with bounds
      const currentRTP = Math.min(99, Math.max(90, 96 + (Math.sin(completionRatio * 10) * 3 * randomFactor)));
      const currentConfidence = Math.min(99, Math.max(85, 90 + (completionRatio * 9)));
      
      // Similarly randomized metrics that converge, with safety bounds
      const currentHitRate = Math.min(35, Math.max(15, 25 + (Math.sin(completionRatio * 5) * 5 * randomFactor)));
      const volatilityTarget = 6; // medium-high
      const currentVolatility = Math.min(10, Math.max(1, volatilityTarget + (Math.sin(completionRatio * 8) * 2 * randomFactor)));
      
      // Convert volatility to feature trigger rate (higher volatility = lower trigger rate)
      const baseFeatureTriggerRate = 1 / (100 + (currentVolatility * 10));
      const currentFeatureTriggerRate = Math.min(0.05, Math.max(0.001, 
        baseFeatureTriggerRate + (Math.sin(completionRatio * 7) * 0.003 * randomFactor)
      ));
      
      // Safely calculate big win rate
      const bigWinRate = Math.min(0.01, Math.max(0.0001, 
        (0.001 * currentVolatility) + (Math.sin(completionRatio * 9) * 0.001 * randomFactor)
      ));
      
      // Enhanced win distribution data - more realistic distribution with 
      // variation based on volatility and completion ratio
      const noWinPercentage = 1 - (currentHitRate / 100); // Convert hit rate to no-win percentage
      
      // Distribution adjustments based on volatility
      // Higher volatility = more big wins, fewer small wins
      let smallWinAdjust = 1.0;
      let mediumWinAdjust = 1.0;
      let bigWinAdjust = 1.0;
      
      if (currentVolatility > 7) { // High volatility
        smallWinAdjust = 0.8;
        mediumWinAdjust = 0.9;
        bigWinAdjust = 1.3;
      } else if (currentVolatility < 4) { // Low volatility 
        smallWinAdjust = 1.2;
        mediumWinAdjust = 1.0;
        bigWinAdjust = 0.7;
      }
      
      // Calculate win distributions
      // Base percentages that will be adjusted (must sum to hit rate)
      const basePercentages = {
        noWin: noWinPercentage,
        small1: 0.15 * smallWinAdjust,
        small2: 0.08 * smallWinAdjust,
        medium1: 0.04 * mediumWinAdjust,
        medium2: 0.02 * mediumWinAdjust,
        big1: 0.006 * bigWinAdjust,
        big2: 0.004 * bigWinAdjust
      };
      
      // Normalize to ensure they sum correctly
      const totalWinPercentage = Object.values(basePercentages).reduce((sum, val) => sum + val, 0) - basePercentages.noWin;
      const targetWinPercentage = currentHitRate / 100;
      const adjustmentFactor = targetWinPercentage / totalWinPercentage;
      
      // Adjusted distribution
      const payoutDistribution = [
        { range: '0x', count: Math.floor(spinsCompleted * basePercentages.noWin), percentage: basePercentages.noWin },
        { range: '1-2x', count: Math.floor(spinsCompleted * basePercentages.small1 * adjustmentFactor), percentage: basePercentages.small1 * adjustmentFactor },
        { range: '3-5x', count: Math.floor(spinsCompleted * basePercentages.small2 * adjustmentFactor), percentage: basePercentages.small2 * adjustmentFactor },
        { range: '6-10x', count: Math.floor(spinsCompleted * basePercentages.medium1 * adjustmentFactor), percentage: basePercentages.medium1 * adjustmentFactor },
        { range: '11-50x', count: Math.floor(spinsCompleted * basePercentages.medium2 * adjustmentFactor), percentage: basePercentages.medium2 * adjustmentFactor },
        { range: '51-100x', count: Math.floor(spinsCompleted * basePercentages.big1 * adjustmentFactor), percentage: basePercentages.big1 * adjustmentFactor },
        { range: '101+x', count: Math.floor(spinsCompleted * basePercentages.big2 * adjustmentFactor), percentage: basePercentages.big2 * adjustmentFactor }
      ];
      
      // Create the current metrics object with safe bounds on all values
      const currentMetrics: SimulationMetrics = {
        rtp: currentRTP,
        rtpConfidence: currentConfidence,
        hitRate: currentHitRate,
        volatility: currentVolatility,
        featureTriggerRate: currentFeatureTriggerRate,
        bigWinRate: bigWinRate,
        maxWin: {
          betMultiplier: Math.floor(500 + (currentVolatility * 100) + (Math.random() * 200)),
          occurred: Math.max(1, Math.floor(spinsCompleted / 100000) + (Math.random() > 0.7 ? 1 : 0))
        },
        symbolDistribution: {
          'WILD': Math.max(0.01, 0.05 - (Math.random() * 0.01)),
          'SCATTER': Math.max(0.005, 0.02 - (Math.random() * 0.005)),
          'H2': Math.max(0.01, 0.08 - (Math.random() * 0.02)),
          'H3': Math.max(0.01, 0.07 - (Math.random() * 0.015)),
          'L1': Math.max(0.05, 0.15 - (Math.random() * 0.03)),
          'L2': Math.max(0.05, 0.17 - (Math.random() * 0.03)),
          'L3': Math.max(0.05, 0.18 - (Math.random() * 0.03))
        },
        payoutDistribution: payoutDistribution,
        spinsCompleted,
        simulationTime: Math.floor(spinsCompleted / 10000) || 1, // Ensure at least 1 second
        warnings: [],
        recommendations: []
      };
      
      // Add warnings based on metrics
      if (Math.abs(currentRTP - 96) > 1) {
        currentMetrics.warnings.push(`RTP of ${currentRTP.toFixed(1)}% deviates from target (96%) by more than 1%`);
      }
      
      if (currentHitRate < 20) {
        currentMetrics.warnings.push(`Hit rate of ${currentHitRate.toFixed(1)}% is lower than recommended (20-30%)`);
      }
      
      // Add recommendations
      if (currentRTP < 95) {
        currentMetrics.recommendations.push('Increase symbol weights for high-paying combinations to reach target RTP');
      } else if (currentRTP > 97) {
        currentMetrics.recommendations.push('Reduce weights of high-paying symbols to align with target RTP');
      }
      
      if (currentHitRate < 22) {
        currentMetrics.recommendations.push('Increase frequency of low-value wins to improve player experience');
      }
      
      if (currentFeatureTriggerRate < 0.005) {
        currentMetrics.recommendations.push('Consider increasing feature trigger frequency for better player retention');
      }
      
      // Only update state if the component is still mounted and running
      if (isRunning) {
        setMetrics(currentMetrics);
      }
    } catch (error) {
      console.error("Error updating simulation metrics:", error);
    }
  };
  
  // Finalize simulation with complete metrics
  const finalizeSimulation = (spinsCompleted: number) => {
    try {
      // Safety check
      if (spinsCompleted <= 0) {
        console.warn("Invalid spin count in finalizeSimulation:", spinsCompleted);
        spinsCompleted = 1000; // Use a fallback value
      }
      
      // Create final metrics with more precise values
      const finalRTP = 95.82; // Slightly off target for demonstration
      const finalHitRate = 24.6; // Hit rate percentage
      const finalVolatility = 6.2; // Medium-high volatility
      const confidence = Math.min(99, 90 + (spinsCompleted / Math.max(1, settings.spinCount)) * 9);
      
      // Calculate realistic win distribution based on final hit rate and volatility
      const noWinPercentage = 1 - (finalHitRate / 100);
      
      // Volatility-based distribution adjustments
      let smallWinAdjust = 1.0;
      let mediumWinAdjust = 1.0;
      let bigWinAdjust = 1.0;
      
      if (finalVolatility > 7) { // High volatility
        smallWinAdjust = 0.8;
        mediumWinAdjust = 0.9;
        bigWinAdjust = 1.3;
      } else if (finalVolatility < 4) { // Low volatility 
        smallWinAdjust = 1.2;
        mediumWinAdjust = 1.0;
        bigWinAdjust = 0.7;
      }
      
      // Calculate distribution percentages
      const basePercentages = {
        noWin: noWinPercentage,
        small1: 0.15 * smallWinAdjust,
        small2: 0.08 * smallWinAdjust,
        medium1: 0.04 * mediumWinAdjust,
        medium2: 0.02 * mediumWinAdjust,
        big1: 0.006 * bigWinAdjust,
        big2: 0.004 * bigWinAdjust
      };
      
      // Normalize to ensure they sum correctly
      const totalWinPercentage = Object.values(basePercentages).reduce((sum, val) => sum + val, 0) - basePercentages.noWin;
      const targetWinPercentage = finalHitRate / 100;
      const adjustmentFactor = targetWinPercentage / totalWinPercentage;
      
      // Final win distribution with proper percentages
      const finalPayoutDistribution = [
        { range: '0x', count: Math.floor(spinsCompleted * basePercentages.noWin), percentage: basePercentages.noWin },
        { range: '1-2x', count: Math.floor(spinsCompleted * basePercentages.small1 * adjustmentFactor), percentage: basePercentages.small1 * adjustmentFactor },
        { range: '3-5x', count: Math.floor(spinsCompleted * basePercentages.small2 * adjustmentFactor), percentage: basePercentages.small2 * adjustmentFactor },
        { range: '6-10x', count: Math.floor(spinsCompleted * basePercentages.medium1 * adjustmentFactor), percentage: basePercentages.medium1 * adjustmentFactor },
        { range: '11-50x', count: Math.floor(spinsCompleted * basePercentages.medium2 * adjustmentFactor), percentage: basePercentages.medium2 * adjustmentFactor },
        { range: '51-100x', count: Math.floor(spinsCompleted * basePercentages.big1 * adjustmentFactor), percentage: basePercentages.big1 * adjustmentFactor },
        { range: '101+x', count: Math.floor(spinsCompleted * basePercentages.big2 * adjustmentFactor), percentage: basePercentages.big2 * adjustmentFactor }
      ];
      
      const finalMetrics: SimulationMetrics = {
        rtp: finalRTP,
        rtpConfidence: confidence,
        hitRate: finalHitRate,
        volatility: finalVolatility,
        featureTriggerRate: 0.008,
        bigWinRate: 0.005,
        maxWin: {
          betMultiplier: 1250,
          occurred: Math.max(1, Math.floor(spinsCompleted / 100000))
        },
        symbolDistribution: {
          'WILD': 0.042,
          'SCATTER': 0.018,
          'H2': 0.072,
          'H3': 0.065,
          'L1': 0.135,
          'L2': 0.155,
          'L3': 0.165
        },
        payoutDistribution: finalPayoutDistribution,
        spinsCompleted,
        simulationTime: Math.floor(spinsCompleted / 8000) || 1, // Ensure at least 1 second
        warnings: [],
        recommendations: []
      };
      
      // Add warnings and recommendations
      if (Math.abs(finalRTP - 96) > 0.5) {
        finalMetrics.warnings.push(`RTP of ${finalRTP.toFixed(2)}% deviates from target (96.00%) by ${Math.abs(finalRTP - 96).toFixed(2)}%`);
      }
      
      // Add detailed recommendations
      finalMetrics.recommendations.push('Increase wild symbol weight by 5% to bring RTP closer to target');
      finalMetrics.recommendations.push('Adjust bonus feature frequency for more consistent player experience');
      finalMetrics.recommendations.push('Consider adding small-win combinations to improve hit frequency');
      
      // Update state in a safer manner with a small timeout to avoid React state update conflicts
      setTimeout(() => {
        if (progressIntervalRef.current === null) { // Only update if interval is already cleared
          setMetrics(finalMetrics);
          setIsComplete(true);
          setIsRunning(false);
          setProgress(100);
        }
      }, 50);
    } catch (error) {
      console.error("Error finalizing simulation:", error);
      // Try to recover gracefully
      setIsRunning(false);
      setIsComplete(false);
    }
  };
  
  // Add a function for users to manually apply simulation results to config
  const applySimulationResults = () => {
    try {
      if (!metrics || !isComplete) {
        console.warn("Cannot apply results: simulation not complete or no metrics available");
        return;
      }
      
      // Create a safe update that doesn't cause circular references
      const configUpdates = {
        rtp: metrics.rtp,
        volatility: metrics.volatility,
        hitRate: metrics.hitRate,
        simulationResults: {
          featureTriggerRate: metrics.featureTriggerRate,
          maxWin: metrics.maxWin,
          lastSimulationTime: new Date().toISOString(),
          spinsCompleted: metrics.spinsCompleted
        }
      };
      
      // Update config safely
      updateConfig(configUpdates);
      
      // Show success message
      alert("Simulation results successfully applied to game configuration!");
    } catch (error) {
      console.error("Error applying simulation results:", error);
      alert("Failed to apply simulation results to configuration. Please try again.");
    }
  };

  // Generate and export detailed simulation report
  const exportSimulationReport = (format = 'json') => {
    try {
      if (!metrics || !isComplete) {
        alert("Cannot export report: simulation not complete or no metrics available");
        return;
      }

      if (format === 'csv') {
        exportFullSimulationCSV();
        return;
      }

      // Generate a comprehensive game round report
      const gameRounds = generateSampleGameRounds();
      
      // Create report sections
      const reportData = {
        title: "Slot Game Simulation Report",
        date: new Date().toLocaleString(),
        summary: {
          rtp: metrics.rtp.toFixed(2) + "%",
          hitRate: metrics.hitRate.toFixed(2) + "%",
          volatility: metrics.volatility.toFixed(1) + " (scale 1-10)",
          featureTriggerRate: "1:" + Math.round(1 / metrics.featureTriggerRate),
          maxWin: metrics.maxWin.betMultiplier + "x bet",
          spinsCompleted: metrics.spinsCompleted.toLocaleString(),
          simulationTime: metrics.simulationTime + " seconds"
        },
        gameRounds: gameRounds,
        rtpComponents: {
          baseGame: "67.2%",
          freeSpins: "19.2%",
          pickAndClick: "6.1%",
          jackpots: "3.5%",
          total: metrics.rtp.toFixed(2) + "%"
        },
        recommendations: metrics.recommendations,
        warnings: metrics.warnings
      };

      // Convert to JSON for download
      const jsonData = JSON.stringify(reportData, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      // Create download link and trigger download
      const a = document.createElement('a');
      a.href = url;
      a.download = 'slot_simulation_report.json';
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);
      
    } catch (error) {
      console.error("Error exporting simulation report:", error);
      alert("Failed to export report. Please try again.");
    }
  };
  
  // Export CSV with full simulation data for all spins
  const exportFullSimulationCSV = () => {
    try {
      if (!metrics || !isComplete) {
        alert("Cannot export CSV: simulation not complete or no metrics available");
        return;
      }
      
      // Show generating message for large simulations
      if (settings.spinCount > 100000) {
        alert(`Generating CSV for ${settings.spinCount.toLocaleString()} spins. This may take a moment...`);
      }
      
      // Set up CSV headers with comments (will be removed from actual CSV)
      const headers = [
        "Timestamp",           // ISO timestamp of the spin
        "GameRoundNumber",     // Sequential game round number
        "BetAmount",           // Amount bet in dollars
        "Win",                 // Amount won in dollars
        "Type",                // Game type: base, freespins, picknclick
        "Status",              // Transaction status: success (97.2%), failed (0.8%) - technical issues only
        "RTP",                 // Running Return to Player percentage
        "Hold"                 // Running hold percentage (100% - RTP)
      ];
      
      // Create CSV content
      let csvContent = headers.join(",") + "\n";
      
      // This is the real requested feature: simulate the actual number of spins rather than just 20
      // Create a statistical simulator based on the metrics
      const totalSpins = settings.spinCount;
      const betAmount = 1.00;
      const hitRate = metrics.hitRate / 100; // Convert percentage to decimal
      const featureTriggerRate = metrics.featureTriggerRate;
      const actualRTP = metrics.rtp / 100; // Convert percentage to decimal
      
      // Feature parameters
      const freeSpinProbability = featureTriggerRate * 0.7; // 70% of features are free spins
      const pickAndClickProbability = featureTriggerRate * 0.3; // 30% of features are pick and click
      
      // Keep track of total amounts for final RTP validation
      let totalBet = 0;
      let totalWin = 0;
      
      // Base date for timestamp simulation
      const baseDate = new Date();
      baseDate.setHours(baseDate.getHours() - (totalSpins / 3600)); // Set back in time to simulate duration
      
      // Batch processing to avoid memory issues with very large simulations
      const batchSize = 10000;
      const batches = Math.ceil(totalSpins / batchSize);
      
      for (let batch = 0; batch < batches; batch++) {
        let batchRows = [];
        const startSpin = batch * batchSize;
        const endSpin = Math.min(startSpin + batchSize, totalSpins);
        
        // Process spins in current batch
        for (let i = startSpin; i < endSpin; i++) {
          const spinNumber = i + 1;
          
          // Simulate timestamp - advancing roughly 1 second per spin
          const timestamp = new Date(baseDate.getTime() + (i * 1000));
          const formattedDate = timestamp.toISOString();
          
          // Determine outcome based on statistical probabilities
          const isWin = Math.random() < hitRate;
          const isFreeSpinTrigger = Math.random() < freeSpinProbability;
          const isPickAndClickTrigger = !isFreeSpinTrigger && Math.random() < pickAndClickProbability;
          
          // Set spin type
          let spinType = "base";
          if (isFreeSpinTrigger) spinType = "freespins";
          if (isPickAndClickTrigger) spinType = "picknclick";
          
          // Determine win amount following the metrics distribution
          let winAmount = 0;
          
          // Win determinations are made separately from whether it's base game or feature
          // A player can win or lose in any game type - winning is based on hitRate
          if (isWin) {
            if (spinType === "base") {
              // Base game wins typically smaller
              winAmount = betAmount * (Math.random() * 5 + 1); // 1-6x bet
            } else if (spinType === "freespins") {
              // Free spins have higher wins
              winAmount = betAmount * (Math.random() * 15 + 5); // 5-20x bet
            } else if (spinType === "picknclick") {
              // Pick and click has medium-high wins
              winAmount = betAmount * (Math.random() * 25 + 10); // 10-35x bet
            }
          } else {
            // No win, but we still record the game type correctly
            winAmount = 0;
          }
          
          // Add some big wins rarely
          if (Math.random() < 0.001) { // 0.1% chance
            winAmount = betAmount * (Math.random() * 100 + 50); // 50-150x bet
          }
          
          // Very rare jackpot-like wins
          if (Math.random() < 0.0001) { // 0.01% chance
            winAmount = betAmount * (Math.random() * 500 + 200); // 200-700x bet
          }
          
          // Track totals for RTP calculation
          totalBet += betAmount;
          totalWin += winAmount;
          
          // Current RTP and hold
          const currentRTP = (totalWin / totalBet) * 100;
          const currentHold = 100 - currentRTP;
          
          // Status is success for almost all transactions, with a very small percentage of failed (technical issues)
          // Only 0.8% of transactions should fail due to technical issues (not related to winning/losing)
          const hasTechnicalFailure = Math.random() < 0.008; // 0.8% failure rate
          const status = hasTechnicalFailure ? "failed" : "success";
          
          // Round values to 2 decimal places for currency
          const formattedWin = winAmount.toFixed(2);
          const formattedRTP = currentRTP.toFixed(2);
          const formattedHold = currentHold.toFixed(2);
          
          // Build CSV row
          const row = [
            formattedDate,
            spinNumber,
            betAmount.toFixed(2),
            formattedWin,
            spinType,
            status,
            formattedRTP,
            formattedHold
          ];
          
          batchRows.push(row.join(","));
        }
        
        // Add batch to CSV content
        csvContent += batchRows.join("\n") + "\n";
      }
      
      // Create and download the CSV file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `slot_simulation_${totalSpins.toLocaleString()}_spins.csv`;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);
      
    } catch (error) {
      console.error("Error exporting CSV simulation data:", error);
      alert("Failed to export CSV. Please try again with fewer spins.");
    }
  };
  
  // Generate sample game rounds for the report with feature hits
  const generateSampleGameRounds = () => {
    const rounds = [];
    const totalRounds = 20; // Show 20 example game rounds
    let currentBet = 1.00;
    let spinNumber = 1;
    let balance = 100.00;
    
    // Feature states
    let inFreeSpins = false;
    let freeSpinsRemaining = 0;
    let inPickAndClick = false;
    
    for (let i = 0; i < totalRounds; i++) {
      // Determine if this is a feature round
      const triggerFreeSpin = !inFreeSpins && Math.random() < 0.15; // Higher chance for example purposes
      const triggerPickAndClick = !inPickAndClick && Math.random() < 0.10;
      
      // Basic round data
      const round = {
        spinNumber: spinNumber++,
        bet: currentBet.toFixed(2),
        balance: balance.toFixed(2),
        outcome: [],
        win: 0,
        features: []
      };
      
      // Handle free spins
      if (inFreeSpins) {
        round.features.push({
          type: "freeSpins",
          spinsRemaining: freeSpinsRemaining - 1,
          multiplier: 2
        });
        freeSpinsRemaining--;
        if (freeSpinsRemaining <= 0) {
          inFreeSpins = false;
        }
      }
      
      // Handle pick and click
      if (inPickAndClick) {
        round.features.push({
          type: "pickAndClick",
          picks: 3,
          prizes: ["5x", "10x", "15x"],
          totalWin: 30
        });
        inPickAndClick = false;
      }
      
      // Randomly set outcome symbols (simplified)
      round.outcome = [
        ["K", "Q", "A", "10", "J"],
        ["Q", "WILD", "J", "A", "10"],
        ["A", "9", "WILD", "K", "Q"]
      ];
      
      // Determine win amount based on scenario
      let winAmount = 0;
      const isWin = Math.random() < 0.3; // 30% win rate for examples
      
      if (isWin) {
        winAmount = currentBet * (Math.random() * 3 + 1); // 1-4x bet for regular wins
        round.winLines = [{
          symbols: inFreeSpins ? "WILD-WILD-WILD" : "A-A-A",
          positions: [[0,2], [1,2], [2,2]],
          pay: winAmount.toFixed(2)
        }];
      }
      
      // Trigger new free spins
      if (triggerFreeSpin) {
        inFreeSpins = true;
        freeSpinsRemaining = 10;
        round.features.push({
          type: "freeSpinsTrigger",
          spinsAwarded: 10,
          triggerSymbols: ["SCATTER", "SCATTER", "SCATTER"]
        });
        // Bonus win for triggering
        winAmount += currentBet * 5;
      }
      
      // Trigger pick and click
      if (triggerPickAndClick) {
        inPickAndClick = true;
        round.features.push({
          type: "pickAndClickTrigger",
          triggerSymbols: ["BONUS", "BONUS", "BONUS"]
        });
      }
      
      // Update round data
      round.win = winAmount.toFixed(2);
      
      // Update balance - no cost for free spins
      if (!inFreeSpins || i === 0) {
        balance -= currentBet;
      }
      balance += winAmount;
      
      rounds.push(round);
    }
    
    return rounds;
  };
  
  return (
    <div className="deep-simulation">
      <h2 className="text-2xl font-bold mb-8 text-center">Deep Simulation System</h2>
      
      <SimulationErrorBoundary>
        <div className="space-y-8">
          {/* Simulation controls and visualization */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
            {/* Simulation settings */}
            <div>
              <SimulationSettings 
                settings={settings}
                onSettingsChange={setSettings}
              />
              
             
            </div>
            
            {/* Simulation visualization */}
            <div>
              <SimulationVisualizer
                isRunning={isRunning}
                settings={settings}
                metrics={metrics}
                progress={progress}
                onStop={stopSimulation}
                onStart={startSimulation}
                isComplete={isComplete}
              />
               <div className="mt-6 flex gap-2"> 
                {!isRunning ? (
                  <button
                    onClick={startSimulation}
                    className="flex-1 py-3 bg-blue-600 text-white rounded-lg flex items-center justify-center gap-2 hover:bg-blue-700"
                  >
                    <PlayCircle className="w-5 h-5" />
                    Run Simulation
                  </button>
                ) : (
                  <button
                    onClick={stopSimulation}
                    className="flex-1 py-3 bg-red-600 text-white rounded-lg flex items-center justify-center gap-2 hover:bg-red-700"
                  >
                    <Pause className="w-5 h-5" />
                    Stop Simulation
                  </button>
                )}
                
                {(isComplete || metrics) && (
                  <button
                    onClick={resetSimulation}
                    className="py-3 px-4 bg-gray-200 text-gray-700 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-300"
                  >
                    <RefreshCw className="w-5 h-5" />
                    Reset
                  </button>
                )}
              </div>
            </div>
          </div>
          
          {/* Simulation results */}
          <div>
            <SimulationResults
              metrics={metrics}
              isComplete={isComplete}
              applySimulationResults={applySimulationResults}
              exportReport={exportSimulationReport}
            />
          </div>
          
          {/* Navigation button with enhanced error handling */}
          <div className="mt-8 flex justify-end">
          </div>
        </div>
      </SimulationErrorBoundary>
    </div>
  );
};

export default DeepSimulation;