import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useGameStore } from '../../../store';
import { Monitor, Smartphone, RotateCcw, Maximize, Minimize, RefreshCw, Palette, Settings } from 'lucide-react';
import { motion } from 'framer-motion';
import UnifiedGridPreview from './UnifiedGridPreview';
import SlotGameUI from '../slot-animation/SlotGameUI';
import MobileLandscapeUI from '../slot-animation/MobileLandscapeUI';
import MobilePortraitUI from '../slot-animation/MobilePortraitUI';
import { useStoredSymbols } from '../../../utils/symbolStorage';

/**
 * GridPreviewWrapper
 * ==================
 * 
 * A professional component for rendering slot game grid previews inside device mockups.
 * 
 * IMPROVEMENTS IMPLEMENTED:
 * 1. Fixed nested mockup rendering issues:
 *    - Eliminated nested .pc-mockup containers
 *    - Created flat DOM hierarchy for all device types
 *    - Removed DOM hierarchy detection that was causing nested rendering
 * 
 * 2. Improved grid scaling for different grid dimensions:
 *    - Dynamic scaling based on grid size (small grids scale up, large grids scale down)
 *    - Perfect centering using flexbox and auto margins
 *    - Consistent proportions across all device types
 * 
 * 3. Enhanced testability and debugging:
 *    - Added comprehensive data attributes (device, orientation, grid dimensions)
 *    - Implemented test IDs for automated testing
 *    - Added validation function to verify scaling behavior
 * 
 * 4. Accessibility improvements:
 *    - Added proper ARIA attributes
 *    - Improved semantic structure
 *    - Ensured proper content flow
 *
 * This component renders the slot grid in these formats:
 * - Desktop browser mockup (landscape only)
 * - Mobile portrait phone mockup
 * - Mobile landscape phone mockup
 */

/**
 * GridPreviewWrapper Component
 * 
 * A professional component for displaying slot game previews in different device mockups:
 * - Desktop (browser frame, always landscape)
 * - Mobile Portrait (vertical phone frame)
 * - Mobile Landscape (horizontal phone frame)
 * 
 * Features:
 * - Auto-scaling based on grid dimensions
 * - Proper device mockups with realistic styling
 * - Consistent labeling that syncs with selected mode
 * - Proper content containment to prevent overflow
 */
interface GridPreviewWrapperProps {
  /** Additional class names */
  className?: string;
  /** Optional zoom level - 'auto' (default), 'fit', or '100%' */
  zoomLevel?: 'auto' | 'fit' | '100%';
  /** Custom SVG string for the spin button */
  spinButtonSvg?: string;
  /** Custom image URL for the spin button */
  spinButtonImageUrl?: string;
  /** Custom scale modifier for grid layout */
  gridScaleModifier?: number;
  /** Vertical offset adjustment for grid positioning */
  verticalOffset?: number;
  /** Mockup dimensions (default: 1340x900px) */
  mockupDimensions?: { width: number; height: number };
  /** Current balance value */
  balance?: number;
  /** Current bet value */
  bet?: number;
  /** Current win value */
  win?: number;
  /** Handler for spin button click */
  onSpin?: () => void;
  /** Whether to show the blue cell backgrounds */
  showCellBackgrounds?: boolean;
}

/**
 * PhoneMockup Component
 * Renders a realistic mobile phone frame in either portrait or landscape orientation
 * directly embeds the grid data instead of nesting UnifiedGridPreview components
 * 
 * This component is specifically designed to maintain a flat DOM hierarchy to prevent
 * nested mockup issues. Each orientation has its own rendering logic to ensure
 * proper display of the slot grid within the phone mockup.
 */
const PhoneMockup: React.FC<{
  orientation: 'portrait' | 'landscape';
  reels: number;
  rows: number;
  payMechanism: string;
  isLoading: boolean;
  spinButtonSvg?: string;
  spinButtonImageUrl?: string;
  balance?: number;
  bet?: number;
  win?: number;
  onSpin?: () => void;
  gridScaleModifier?: number;
  verticalOffset?: number;
  showUnifiedUI?: boolean;
  framePath?: string | null;
  framePosition?: { x: number; y: number };
  frameScale?: number;
  frameStretch?: { x: number; y: number };
  backgroundPath?: string | null;
  showCellBackgrounds?: boolean;
}> = ({ 
  orientation, 
  reels, 
  rows, 
  payMechanism, 
  isLoading,
  spinButtonSvg,
  spinButtonImageUrl,
  balance = 1000,
  bet = 1.00,
  win = 0.00,
  onSpin = () => console.log('Spin clicked'),
  gridScaleModifier = 0.94,
  verticalOffset = 0,
  showUnifiedUI = true,
  framePath = null,
  framePosition = { x: 0, y: 0 },
  frameScale = 100,
  frameStretch = { x: 100, y: 100 },
  backgroundPath = null,
  showCellBackgrounds = true
}) => {
  // Determine if we're in portrait mode
  const isPortrait = orientation === 'portrait';
  
  // Different styles based on orientation
  const phoneStyles = isPortrait
    ? {
        width: '100%',
        maxWidth: '360px',
        height: '720px',
        borderRadius: '36px',
      }
    : {
        width: '720px',
        maxWidth: '100%',
        height: '360px',
        borderRadius: '36px',
      };

  // Calculate the scale factor for large grids to prevent overflow
  const calculateGridScale = (): number => {
    // Apply more aggressive scaling in portrait mode for wide grids
    if (isPortrait) {
      // For wider grids (5+ reels in portrait), scale down more aggressively
      if (reels >= 5) return Math.min(1, 5 / reels * 0.8);
      // For taller grids (4+ rows in portrait), scale down more aggressively
      if (rows >= 4) return Math.min(1, 3 / rows * 0.8);
      // For very large grids, scale down even more
      if (reels >= 5 && rows >= 4) return Math.min(1, (5 / reels) * (3 / rows) * 0.75);
    } else {
      // In landscape mode, we have more horizontal space
      if (reels > 5) return Math.min(1, 5 / reels * 0.9);
      if (rows > 3) return Math.min(1, 3 / rows * 0.9);
      if (reels > 5 && rows > 3) return Math.min(1, (5 / reels) * (3 / rows) * 0.85);
    }
    
    // Default scale if none of the above conditions met
    return 1;
  };

  const gridScale = calculateGridScale();
  
  // PORTRAIT PHONE MOCKUP
  if (isPortrait) {
    return (
      <div 
        className="phone-mockup portrait relative bg-black shadow-xl overflow-hidden flex items-center justify-center"
        style={phoneStyles}
        data-mockup-type="phone"
        data-orientation="portrait"
        data-device="mobile"
        data-grid={`${reels}x${rows}`}
        data-testid="phone-portrait-mockup"
      >
        {/* Phone notch */}
        <div 
          className="absolute top-0 left-1/2 transform -translate-x-1/2 w-1/4 h-[28px] bg-black z-20 rounded-b-xl"
          style={{ boxShadow: 'inset 0 -2px 8px rgba(255,255,255,0.1)' }}
        >
          <div className="absolute left-1/2 top-[10px] transform -translate-x-1/2 w-[8px] h-[8px] rounded-full bg-gray-500"></div>
        </div>
        
        {/* Side buttons */}
        <div className="absolute left-0 top-1/4 h-16 w-[4px] bg-gray-700 rounded-r-full"></div>
        <div className="absolute right-0 top-1/4 h-12 w-[4px] bg-gray-700 rounded-l-full"></div>
        <div className="absolute right-0 top-[45%] h-16 w-[4px] bg-gray-700 rounded-l-full"></div>
        
        {/* Phone screen with flex column layout for proper stacking */}
        <div className="screen w-full h-full flex flex-col bg-[#051425] pt-[40px] pb-[20px] px-[15px]">
          {/* Simplified header */}
          <div className="h-[20px] bg-gradient-to-r from-blue-900/80 to-indigo-900/80 rounded-t-lg shrink-0"></div>
          
          {/* Content area with the grid preview - flat container structure */}
          <div className="flex-grow bg-[#041022] flex flex-col relative overflow-hidden">
            {/* Main grid container */}
            <div className="flex-grow flex items-center justify-center p-2">
              <div className="w-full h-full flex items-center justify-center max-w-[92%] max-h-[92%]">
                <UnifiedGridPreview 
                  reels={reels}
                  rows={rows}
                  orientation={orientation}
                  animate={!isLoading}
                  payMechanism={payMechanism}
                  showDebugLabel={false}
                  id="phone-portrait-grid"
                  scaleToFit={true}
                  gridScaleModifier={gridScaleModifier}
                  verticalOffset={verticalOffset}
                  showUnifiedUI={false} // Don't show UI from UnifiedGridPreview
                  spinButtonSvg={spinButtonSvg}
                  spinButtonImageUrl={spinButtonImageUrl}
                  balance={balance}
                  bet={bet}
                  win={win}
                  onSpin={onSpin}
                  framePath={framePath}
                  framePosition={framePosition}
                  frameScale={frameScale}
                  frameStretch={frameStretch}
                  backgroundPath={backgroundPath}
                  showCellBackgrounds={showCellBackgrounds}
                />
              </div>
            </div>
            
            {/* UI controls from MobilePortraitUI */}
            {showUnifiedUI && (
              <div className="absolute inset-0 z-30 pointer-events-none">
                <MobilePortraitUI
                  onSpin={onSpin}
                  onAutoplayToggle={() => console.log('Autoplay toggled')}
                  onMenuToggle={() => console.log('Menu clicked')}
                  onSoundToggle={() => console.log('Sound toggled')}
                  onBetChange={() => console.log('Bet changed')}
                  balance={balance}
                  bet={bet}
                  win={win}
                  className="pointer-events-auto"
                />
              </div>
            )}
          </div>
          
          {/* Footer with simple visual decoration */}
          <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-b-lg shrink-0 h-[20px]"></div>
        </div>
      </div>
    );
  }
  
  // LANDSCAPE PHONE MOCKUP
  return (
    <div 
      className="phone-mockup landscape relative bg-black shadow-xl overflow-hidden flex items-center justify-center"
      style={phoneStyles}
      data-mockup-type="phone"
      data-orientation="landscape"
      data-device="mobile"
      data-grid={`${reels}x${rows}`}
      data-testid="phone-landscape-mockup"
    >
      {/* Power button on top */}
      <div className="absolute top-0 right-1/3 transform -translate-y-[2px] w-[40px] h-[4px] bg-gray-600 rounded-t-full"></div>
      
      {/* Side buttons */}
      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 h-[30%] w-[3px] flex flex-col space-y-5 pl-[1px]">
        <div className="w-[3px] h-[40px] bg-gray-600 rounded-l-full"></div>
        <div className="w-[3px] h-[40px] bg-gray-600 rounded-l-full"></div>
      </div>
      
      {/* Phone screen in landscape mode with flex-column structure */}
      <div className="screen w-full h-full flex flex-row bg-[#051425] p-[12px]">
        {/* Left controls for landscape mode */}
        <div className="w-[20px] bg-gradient-to-b from-gray-900 to-gray-800 rounded-l-lg mr-2 shrink-0">
          {/* Simplified left bar */}
        </div>
        
        {/* Main content area with the grid preview - flex column for proper stacking */}
        <div className="flex-grow bg-[#041022] flex flex-col items-center justify-center overflow-hidden rounded-r-lg relative">
          {/* Main content area for grid */}
          <div className="w-full h-full flex-grow flex items-center justify-center">
            <div className="w-full h-full flex items-center justify-center max-w-[92%] max-h-[92%]">
              <UnifiedGridPreview 
                reels={reels}
                rows={rows}
                orientation={orientation}
                animate={!isLoading}
                payMechanism={payMechanism}
                showDebugLabel={false}
                id="phone-landscape-grid"
                scaleToFit={true}
                gridScaleModifier={gridScaleModifier}
                verticalOffset={verticalOffset}
                showUnifiedUI={false} // Don't show UI from UnifiedGridPreview
                spinButtonSvg={spinButtonSvg}
                spinButtonImageUrl={spinButtonImageUrl}
                balance={balance}
                bet={bet}
                win={win}
                onSpin={onSpin}
                framePath={framePath}
                framePosition={framePosition}
                frameScale={frameScale}
                frameStretch={frameStretch}
                backgroundPath={backgroundPath}
                showCellBackgrounds={showCellBackgrounds}
              />
            </div>
          </div>
          
          {/* UI controls from MobileLandscapeUI */}
          {showUnifiedUI && (
            <div className="absolute inset-0 z-30 pointer-events-none">
              <MobileLandscapeUI
                onSpin={onSpin}
                onAutoplayToggle={() => console.log('Autoplay toggled')}
                onMenuToggle={() => console.log('Menu clicked')}
                onSoundToggle={() => console.log('Sound toggled')}
                onBetChange={() => console.log('Bet changed')}
                balance={balance}
                bet={bet}
                win={win}
                className="pointer-events-auto"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * DesktopMockup Component
 * Renders a simplified browser-like frame for desktop preview
 * with direct integration of the grid data instead of nested components
 * 
 * This component is specifically designed to avoid nesting issues by using
 * a flat hierarchy and direct rendering of UnifiedGridPreview.
 * 
 * Key features:
 * - Dynamic scaling based on grid dimensions (larger grids scale down, small grids scale up)
 * - Perfect centering of grid content using flexbox and auto margins
 * - Flat DOM structure with no nested mockups or iframes
 * - Proper data attributes for testing and debugging
 * - Accessible with proper ARIA attributes
 * 
 * Scaling behavior:
 * - 3x3, 4x3: Scaled up slightly (1.1x) for better visibility
 * - 5x3: Standard size (1.0x)
 * - 5x4, 6x3: Slightly scaled down (0.95x)
 * - 6x4, 6x5: Further scaled down (0.9x)
 * - 7x5 and larger: Maximally scaled down (0.85x)
 */
const DesktopMockup: React.FC<{
  reels: number;
  rows: number;
  payMechanism: string;
  isLoading: boolean;
  zoomLevel?: string;
  spinButtonSvg?: string;
  spinButtonImageUrl?: string;
  balance?: number;
  bet?: number;
  win?: number;
  onSpin?: () => void;
  gridScaleModifier?: number;
  verticalOffset?: number;
  mockupDimensions?: { width: number; height: number };
  framePath?: string | null;
  framePosition?: { x: number; y: number };
  frameScale?: number;
  frameStretch?: { x: number; y: number };
  backgroundPath?: string | null;
  showCellBackgrounds?: boolean;
}> = ({ 
  reels, 
  rows, 
  payMechanism, 
  isLoading, 
  zoomLevel = 'auto',
  spinButtonSvg,
  spinButtonImageUrl,
  balance = 1000,
  bet = 1.00,
  win = 0.00,
  onSpin = () => console.log('Spin clicked'),
  gridScaleModifier = 0.94,
  verticalOffset = 0,
  mockupDimensions = { width: 1340, height: 900 },
  framePath = null,
  framePosition = { x: 0, y: 0 },
  frameScale = 100,
  frameStretch = { x: 100, y: 100 },
  backgroundPath = null,
  showCellBackgrounds = true
}) => {
  // Override and define our own scaling function inside this component
  // to avoid scope issues with the outer component
  const getScale = (): number => {
    // Basic scale factor based on grid size
    if (reels <= 3 && rows <= 3) return 1.2;  // Small grid
    if (reels <= 5 && rows <= 3) return 1.1;  // Standard grid
    return 1.0;  // Larger grid
  };
  // Refined scaling logic for optimal grid proportions based on dimensions
  /**
   * getDesktopGridScaleFactor - Calculate optimal scaling for different grid configurations
   * 
   * This function implements a sophisticated scaling algorithm that ensures all grid configurations 
   * are displayed at their optimal size, maximizing screen utilization while maintaining proportion.
   * The scaling factors have been finely calibrated for tier 1 AAA-quality slot game visuals based on
   * industry standard proportions observed in top commercial slot games.
   * 
   * Key principles:
   * - Standard 5x3 grid is the baseline (1.0 scale)
   * - Smaller grids (3x3, 4x3) are scaled up significantly for better visibility
   * - Medium grids (5x4, 6x3) receive slight scaling adjustments for optimal appearance
   * - Larger grids are scaled down just enough to fit properly, but never below minimum threshold
   * - Each grid configuration has a precisely calibrated scale factor for maximum visual appeal
   */
  const getDesktopGridScaleFactor = (reels: number, rows: number): number => {
    // Calculate grid density (total cells)
    const gridDensity = reels * rows;
    
    // Define absolute scale limits - adjusted to prevent UI overflow
    const maxScale = 1.2;   // Reduced from 1.35 to prevent UI overflow
    const minScale = 0.88;  // Minimum scale factor to ensure visibility
    
    // Return value within safe bounds
    const applyScaleLimits = (scale: number): number => {
      return Math.min(maxScale, Math.max(minScale, scale));
    };
    
    // Special case configurations with precisely calibrated scaling
    
    // 3x3 grid (special case with reduced scale to prevent UI overflow)
    if (reels === 3 && rows === 3) {
      // More conservative scaling for 3x3 to prevent UI overflow
      return applyScaleLimits(1.18);  // Reduced from 1.35 to 1.18
    }
    
    // Other small grids (with reduced scaling)
    if (reels === 3 && rows === 4) return applyScaleLimits(1.16);  // Reduced from 1.3
    if (reels === 4 && rows === 3) return applyScaleLimits(1.14);  // Reduced from 1.25
    if (reels === 4 && rows === 4) return applyScaleLimits(1.12);  // Reduced from 1.2
    
    // Standard grids - baseline size with slight enhancements
    if (reels === 5 && rows === 3) return 1.1;  // Standard 5x3
    
    // Medium-large grids - optimized scaling
    if (reels === 5 && rows === 4) return applyScaleLimits(1.06);  // 5x4 (reduced from 1.1)
    if (reels === 6 && rows === 3) return applyScaleLimits(1.04);  // 6x3 (reduced from 1.07)
    if (reels === 6 && rows === 4) return applyScaleLimits(1.02);  // 6x4 (reduced from 1.05)
    
    // Large grids - progressively scaled based on density with improved visibility
    if (reels === 7 && rows <= 4) return applyScaleLimits(0.98);  // 7-reel medium height (reduced from 1.0)
    if (reels === 7 && rows >= 5) return applyScaleLimits(0.96);  // 7-reel tall (reduced from 0.97)
    if (reels === 8) return applyScaleLimits(0.94);               // 8-reel configs (reduced from 0.95)
    if (reels >= 9) return applyScaleLimits(0.9);                 // 9+ reel configs
    
    // For non-standard configurations, calculate based on grid density
    if (gridDensity > 35) return applyScaleLimits(0.9);           // Extremely dense grids (> 35 cells)
    if (gridDensity > 30) return applyScaleLimits(0.94);          // Very dense grids (31-35 cells)
    if (gridDensity > 25) return applyScaleLimits(0.97);          // Dense grids (26-30 cells)
    if (gridDensity > 20) return applyScaleLimits(1.0);           // Medium-dense grids (21-25 cells)
    if (gridDensity > 15) return applyScaleLimits(1.05);          // Standard density (16-20 cells)
    if (gridDensity > 12) return applyScaleLimits(1.1);           // Small grids (13-15 cells)
    if (gridDensity <= 12) return applyScaleLimits(1.15);         // Very small grids (≤ 12 cells)
    
    // Default for other configurations
    return 1.0;
  };
  
  // For backward compatibility
  const getScaleFactor = getDesktopGridScaleFactor;
  
  // Store symbols in ref to prevent unnecessary renders
  const symbolCacheRef = useRef<string[]>([]);
  
  // Validate scale factor against test grid configurations (simplified version)
  const validateScaleFactor = (): string => {
    // Updated with improved scaling values to prevent UI overflow (May 2024)
    const testConfigs = [
      { reels: 3, rows: 3, expected: 1.18, name: 'Classic 3×3' },
      { reels: 3, rows: 4, expected: 1.16, name: 'Tall 3×4' },
      { reels: 4, rows: 3, expected: 1.14, name: 'Small 4×3' },
      { reels: 4, rows: 4, expected: 1.12, name: 'Square 4×4' },
      { reels: 5, rows: 3, expected: 1.1, name: 'Standard 5×3' },
      { reels: 5, rows: 4, expected: 1.06, name: 'Extended 5×4' },
      { reels: 6, rows: 3, expected: 1.04, name: 'Wide 6×3' },
      { reels: 6, rows: 4, expected: 1.02, name: 'Extended wide 6×4' },
      { reels: 7, rows: 4, expected: 0.98, name: 'Wide 7×4' },
      { reels: 7, rows: 5, expected: 0.96, name: 'Very large 7×5' },
      { reels: 8, rows: 6, expected: 0.94, name: 'Massive 8×6' },
      { reels: 9, rows: 5, expected: 0.9, name: 'Extra wide 9×5' },
    ];
    
    // Find the current configuration
    const currentConfig = testConfigs.find(config => 
      config.reels === reels && config.rows === rows);
      
    if (currentConfig) {
      // Get base scale
      const baseScale = getScaleFactor(reels, rows);
      
      return `${currentConfig.name}: ${Math.abs(baseScale - currentConfig.expected) < 0.01 ? '✓' : '✗'} 
             (expected ${currentConfig.expected}, base ${baseScale} with ${zoomLevel} zoom)`;
    }
    
    return `Custom ${reels}×${rows}: scale=${getScaleFactor(reels, rows)}`;
  };
  
  // Disable debug messages for now to avoid errors
  // if (process.env.NODE_ENV === 'development') {
  //   try {
  //     console.debug(`Grid scaling for ${reels}×${rows}`);
  //   } catch (e) {
  //     console.debug(`Grid scaling debug error: ${e.message}`);
  //   }
  // }

  const gridScale = getScaleFactor(reels, rows);
  
  // Single load indicator to prevent duplicate symbol loading
  const [symbolsAlreadyLoaded, setSymbolsAlreadyLoaded] = useState(false);
  
  // Run on component mount - simplified to prevent multiple redraws
  useEffect(() => {
    // Only run this once per component instance
    if (symbolsAlreadyLoaded) return;
    
    console.log('GridPreviewWrapper mounted - single initial symbol load');
    
    // Check for symbols in the store first
    const { config } = useGameStore.getState();
    if (config?.theme?.generated?.symbols && config.theme.generated.symbols.length > 0) {
      console.log('Found symbols in store on mount:', config.theme.generated.symbols.length);
      
      // Cache the symbols for future use
      symbolCacheRef.current = [...config.theme.generated.symbols];
      
      // Single dispatch with symbols from store
      window.dispatchEvent(new CustomEvent('symbolsChanged', {
        detail: {
          symbols: symbolCacheRef.current,
          source: 'gridPreviewWrapperMount'
        }
      }));
      
      // Mark as loaded to prevent duplicate loads
      setSymbolsAlreadyLoaded(true);
    } else {
      // Request symbols just once if none in store
      window.dispatchEvent(new CustomEvent('requestSymbols'));
      
      // Schedule a follow-up check after a short delay
      setTimeout(() => {
        const { config } = useGameStore.getState();
        if (config?.theme?.generated?.symbols && config.theme.generated.symbols.length > 0 && !symbolsAlreadyLoaded) {
          console.log('Follow-up check found symbols in store:', config.theme.generated.symbols.length);
          symbolCacheRef.current = [...config.theme.generated.symbols];
          window.dispatchEvent(new CustomEvent('symbolsChanged', {
            detail: {
              symbols: symbolCacheRef.current,
              source: 'gridPreviewWrapperFollowUp'
            }
          }));
          setSymbolsAlreadyLoaded(true);
        }
      }, 300);
    }
  }, [symbolsAlreadyLoaded]);
  
  // Define optimal mockup dimensions based on grid configuration and mockupDimensions prop
  const getMockupDimensions = () => {
    // Grid-specific configurations for optimal display
    const is3x3 = reels === 3 && rows === 3;
    const is3x4 = reels === 3 && rows === 4;
    const is4x3 = reels === 4 && rows === 3;
    const is4x4 = reels === 4 && rows === 4;
    const isWideGrid = reels >= 7;
    const isTallGrid = rows >= 5;
    
    // Start with the prop dimensions as base
    let baseWidth = mockupDimensions.width;
    let baseHeight = mockupDimensions.height;
    
    // Adjust aspect ratio based on grid type
    let aspectRatio;
    
    // Calculate aspect ratio based on grid dimensions
    if (is3x3) {
      aspectRatio = '4/3';  // More square for 3x3
    } else if (is3x4) {
      aspectRatio = '4/3';  // Similar to 3x3 but slightly taller
    } else if (is4x3) {
      aspectRatio = '16/10'; // Slightly wider for 4x3
    } else if (isWideGrid) {
      aspectRatio = '16/9'; // Widescreen for wide grids
    } else {
      aspectRatio = baseWidth / baseHeight; // Use dimensions ratio
    }
    
    /**
     * Calculate height based on the appropriate aspect ratio
     * to ensure consistent proportions across all grid types.
     * 
     * 16:9 = 1.78:1
     * 4:3 = 1.33:1
     * 16:10 = 1.6:1
     */
    if (aspectRatio === '16/9') {
      baseHeight = baseWidth / 1.78;
    } else if (aspectRatio === '4/3') {
      baseHeight = baseWidth / 1.33;
    } else if (aspectRatio === '16/10') {
      baseHeight = baseWidth / 1.6;
    }
    
    // Fine-tune dimensions for specific grid sizes
    if (is3x3) {
      // 3x3 should have more square proportions - use smaller width
      baseWidth = Math.min(baseWidth, 1240);
      baseHeight = baseWidth / 1.33; // 4:3 ratio
    } else if (isWideGrid) {
      // Wide grids need more width relative to height
      baseHeight = Math.min(baseHeight, 880);
    }
    
    // Convert to string values for style props
    const finalWidth = `${Math.round(baseWidth)}px`;
    const finalHeight = `${Math.round(baseHeight)}px`;
    
    // Debug info
    console.debug(`Mockup dimensions for ${reels}×${rows}: ${finalWidth}×${finalHeight}, aspectRatio: ${aspectRatio}`);
    
    // Return style object to be spread into the component
    return {
      width: finalWidth,
      maxWidth: '100%',
      height: finalHeight,
      maxHeight: '95vh',
      minHeight: '600px',
      aspectRatio: aspectRatio,
      boxShadow: '0 30px 60px -15px rgba(0, 0, 0, 0.3), 0 15px 30px -10px rgba(0, 0, 0, 0.2)',
      borderRadius: '1rem',
      overflow: 'hidden',
    };
  };
  
  // Get dimensions for current grid configuration
  const calculatedMockupDimensions = getMockupDimensions();
  
  // Simplified browser mockup with flattened structure and improved accessibility
  // Add additional wrapper for small grids to prevent UI overflow
  const isSmallGrid = reels <= 4 && rows <= 4;
  
  return (
    <div 
      className="desktop-mockup flex flex-col rounded-lg overflow-hidden bg-gradient-to-b from-gray-900 to-gray-950 border border-gray-700 mx-auto my-auto"
      style={{
        ...calculatedMockupDimensions,
        // Add scaling container for small grids
        transform: isSmallGrid ? 'scale(0.95)' : 'none',
        transformOrigin: 'center'
      }}
      data-mockup-type="browser"
      data-orientation="landscape"
      data-device="desktop"
      data-grid={`${reels}x${rows}`}
      data-testid="desktop-preview"
      aria-label={`Desktop preview of ${reels}×${rows} slot grid`}
      role="img"
    >
      {/* Premium gaming browser-like top bar with modern styling */}
      <div className={`${isSmallGrid ? 'h-14' : 'h-16'} bg-gradient-to-r from-gray-950 via-gray-900 to-gray-950 flex items-center px-8 shrink-0 border-b border-gray-700/80 shadow-md relative overflow-hidden`}>
        {/* Subtle animation effect on the browser chrome */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute inset-0 opacity-10 bg-grid-pattern"></div>
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent"></div>
        </div>
        
        {/* Traffic light controls */}
        <div className="flex space-x-3 z-10">
          <div className="w-4 h-4 rounded-full bg-red-500 shadow-inner shadow-red-700 hover:bg-red-600 cursor-pointer transition-colors"></div>
          <div className="w-4 h-4 rounded-full bg-yellow-500 shadow-inner shadow-yellow-700 hover:bg-yellow-600 cursor-pointer transition-colors"></div>
          <div className="w-4 h-4 rounded-full bg-green-500 shadow-inner shadow-green-700 hover:bg-green-600 cursor-pointer transition-colors"></div>
        </div>
        
        {/* Gaming URL bar with secure icon */}
        <div className="mx-auto px-5 py-2 bg-gradient-to-r from-gray-800/80 to-gray-900/80 backdrop-blur-md rounded-md text-white text-sm flex items-center shadow-inner shadow-black/30 min-w-[420px] border border-gray-700/70 hover:border-gray-600/70 transition-colors group">
          {/* Secure icon */}
          <div className="flex items-center justify-center mr-2 text-green-400 group-hover:text-green-300 transition-colors">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          
          {/* Domain with subtle hover effect */}
          <div className="flex items-center">
            <span className="text-gray-400 mr-1">https://</span>
            <span className="font-medium text-blue-300 group-hover:text-blue-200 transition-colors">premium-slot</span>
            <span className="text-gray-400">.gamecrafter.com</span>
            <span className="ml-2 text-gray-500 text-xs">/play</span>
          </div>
        </div>
        
        {/* Browser action buttons */}
        <div className="flex space-x-4 ml-4 z-10">
          <div className="w-8 h-8 rounded-full flex items-center justify-center bg-gray-800/50 border border-gray-700/50 hover:bg-gray-700/50 transition-colors cursor-pointer">
            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
            </svg>
          </div>
          <div className="w-8 h-8 rounded-full flex items-center justify-center bg-gray-800/50 border border-gray-700/50 hover:bg-gray-700/50 transition-colors cursor-pointer">
            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
        </div>
      </div>
      
      {/* Enhanced content area with optimized grid display for premium look */}
      <div className="flex-grow flex items-center justify-center bg-gradient-to-b from-[#041022] to-[#081c36] overflow-hidden p-4">
        {/* Background elements for AAA visual quality */}
        <div className="absolute inset-0 overflow-hidden opacity-40 pointer-events-none">
          <div className="absolute w-full h-full">
            {/* Light beams animation */}
            <div className="absolute top-1/4 left-0 w-[400px] h-[800px] bg-blue-500/5 rotate-[25deg] blur-3xl transform -translate-x-1/2"></div>
            <div className="absolute bottom-1/4 right-0 w-[300px] h-[600px] bg-indigo-500/5 -rotate-[25deg] blur-3xl transform translate-x-1/2"></div>
          </div>
        </div>
        
        {/* Game canvas container with perfect centering */}
        <div 
          className="game-preview-wrapper flex-grow flex flex-col items-center justify-center relative w-full h-full"
        >
          {/* Grid preview container */}
          <div className="grid-preview-container w-full flex-grow flex items-center justify-center relative bg-gradient-to-b from-[#041022]/80 to-[#061830]/80 p-3">
            <div 
              className="grid-preview-scaled w-full h-full flex items-center justify-center"
              style={{ 
                transform: 'scale(0.95)',
                transformOrigin: 'center',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                position: 'relative'
              }}
              data-grid-dimensions={`${reels}x${rows}`}
              data-grid-scale={gridScale}
              data-zoom-level={zoomLevel}
            >
              {/* Enhanced semi-transparent background glow for visual emphasis */}
              <div 
                className="absolute rounded-2xl -z-10 opacity-25 blur-2xl animate-pulse-slow" 
                style={{
                  width: '160%',
                  height: '160%',
                  background: 'radial-gradient(circle, rgba(59,130,246,0.4) 0%, rgba(79,70,229,0.2) 35%, rgba(16,24,39,0) 70%)',
                  transform: 'translate(-50%, -50%)',
                  left: '50%',
                  top: '50%'
                }}
              />
              
              {/* Optional grid outline for visual distinction */}
              <div 
                className="absolute inset-[-5px] rounded-lg z-[-5] opacity-30" 
                style={{
                  boxShadow: "inset 0 0 30px 5px rgba(59,130,246,0.3)",
                }}
              />
              
              <UnifiedGridPreview 
                reels={reels}
                rows={rows}
                orientation="landscape" // Desktop is always landscape
                animate={!isLoading}
                payMechanism={payMechanism}
                showDebugLabel={false}
                id="desktop-grid-preview"
                scaleToFit={true}
                gridScaleModifier={gridScaleModifier}
                verticalOffset={verticalOffset}
                spinButtonSvg={spinButtonSvg}
                spinButtonImageUrl={spinButtonImageUrl}
                balance={balance}
                bet={bet}
                win={win}
                onSpin={onSpin}
                showUnifiedUI={true}
                framePath={framePath}
                framePosition={framePosition}
                frameScale={frameScale}
                frameStretch={frameStretch}
                backgroundPath={backgroundPath}
                showCellBackgrounds={showCellBackgrounds}
              />
            </div>
          </div>
          
        </div>
      </div>
    </div>
  );
};

/**
 * Main GridPreviewWrapper Component
 *
 * This component handles the visualization of the slot grid inside different device mockups.
 * It supports switching between:
 * - Mobile portrait mode (phone mockup in vertical orientation)
 * - Mobile landscape mode (phone mockup in horizontal orientation)
 * - Desktop mode (browser-like frame in landscape orientation only)
 *
 * Key features:
 * - Device type switching (mobile/desktop)
 * - Orientation toggling for mobile devices
 * - Auto-scaling based on grid dimensions
 * - Event propagation to ensure other components are aware of changes
 * - Dynamic sizing with 16:9 aspect ratio for desktop view
 * - Optimized scaling factors for all grid configurations
 *
 * The component architecture avoids nested mockups by using conditional rendering
 * with separate components for each device type.
 *
 * Future enhancements (planned):
 * - Zoom toggle feature with preset options:
 *   - Auto (default, uses intelligent scaling based on grid size)
 *   - Fit to Screen (ensures entire grid is visible)
 *   - 100% (displays at exact 1:1 scale)
 * - Tablet device mockup support
 * - Win animation previews
 */
const GridPreviewWrapper: React.FC<GridPreviewWrapperProps> = ({
  className = '',
  zoomLevel: initialZoomLevel = 'auto',
  spinButtonSvg,
  spinButtonImageUrl,
  gridScaleModifier = 0.94, // Default scale modifier
  verticalOffset = 0,
  mockupDimensions = { width: 1340, height: 900 }, // Default size for desktop view
  balance = 1000,
  bet = 1.00,
  win = 0.00,
  onSpin = () => console.log('Spin clicked'),
  showCellBackgrounds
}) => {
  // Check if we're on Step 5 to show preset/advanced toggle
  const [viewMode, setViewMode] = useState<'preset' | 'advanced'>('preset');
  
  // Effect to add listeners for Step 5 view mode sync
  useEffect(() => {
    // Listen for view mode changes from Step5_GameFrameDesigner
    const handleStep5ViewModeChanged = (event: Event) => {
      try {
        const customEvent = event as CustomEvent;
        if (customEvent.detail && customEvent.detail.mode) {
          const newMode = customEvent.detail.mode;
          if (newMode === 'preset' || newMode === 'advanced') {
            setViewMode(newMode as 'preset' | 'advanced');
          }
        }
      } catch (err) {
        console.error('Error handling view mode change:', err);
      }
    };

    window.addEventListener('step5ViewModeChanged', handleStep5ViewModeChanged);
    
    return () => {
      window.removeEventListener('step5ViewModeChanged', handleStep5ViewModeChanged);
    };
  }, []);
  
  // Get current grid configuration from store
  const { config } = useGameStore();
  const reels = config.reels?.layout?.reels || 5;
  const rows = config.reels?.layout?.rows || 3;
  const storeOrientation = config.reels?.layout?.orientation || 'landscape';
  const payMechanism = config.reels?.payMechanism || 'betlines';
  
  // Get frame, background, and cell display data from store
  // Read showSymbolBackgrounds from config if not provided as prop
  const cellBackgrounds = showCellBackgrounds !== undefined ? 
    showCellBackgrounds : 
    config.showSymbolBackgrounds !== false;
  const framePath = config.frame || null;
  const backgroundPath = config.background?.backgroundImage || config.backgroundImage || null;
  
  // Frame position and scale data
  const framePosition = config.framePosition || { x: 0, y: 0 };
  const frameScale = config.frameScale || 100;
  const frameStretch = config.frameStretch || { x: 100, y: 100 };

  // Local state for device type and loading transitions
  const [deviceType, setDeviceType] = useState<'mobile' | 'desktop'>('mobile');
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>(storeOrientation);
  const [isLoading, setIsLoading] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // Zoom level toggle state with full implementation structure
  type ZoomLevel = 'auto' | 'fit' | '100%';
  const [zoomLevel, setZoomLevel] = useState<ZoomLevel>(initialZoomLevel);
  
  // Apply zoom level to scale factor calculation
  const applyZoomLevel = (baseScale: number): number => {
    switch(zoomLevel) {
      case 'auto':
        // Auto uses the sophisticated scaling algorithm
        return baseScale;
      case 'fit':
        // Fit ensures everything is visible by selecting a scale
        // that makes the grid fit perfectly in the container
        // This would need actual container measurements in a full implementation
        return Math.min(baseScale, 0.95);
      case '100%':
        // Fixed 1:1 scale regardless of grid size
        return 1.0;
      default:
        return baseScale;
    }
  };
  
  // Helper for zoom level feature UI
  const getZoomLevelLabel = (): string => {
    switch(zoomLevel) {
      case 'auto':
        return 'Auto Scale';
      case 'fit':
        return 'Fit to Screen';
      case '100%':
        return '100% Size';
      default:
        return 'Auto Scale';
    }
  };
  
  // Get the grid scale for display - safe fallback to avoid errors
  const getDisplayScale = (): number => {
    try {
      // Use the already calculated gridScale from getScaleFactor
      const baseScale = getScaleFactor(reels, rows);
      return applyZoomLevel(baseScale);
    } catch (e) {
      // Safe fallback if any error occurs
      return 1.0;
    }
  };
  
  // Container ref for measuring dimensions
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Ensure orientation syncs with store
  useEffect(() => {
    setOrientation(storeOrientation);
  }, [storeOrientation]);
  
  // Update zoom level if prop changes
  useEffect(() => {
    if (initialZoomLevel !== zoomLevel) {
      setZoomLevel(initialZoomLevel);
    }
  }, [initialZoomLevel]);
  
  // Update container and handle window resize
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        // Force a re-render to ensure proper centering and scaling
        setIsLoading(true);
        setTimeout(() => setIsLoading(false), 50);
      }
    };
    
    // Set up resize observer for responsive updates
    const resizeObserver = new ResizeObserver(updateSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    
    // Also listen for window resize events
    window.addEventListener('resize', updateSize);
    
    // Get symbols directly from store for immediate display
    const { config } = useGameStore.getState();
    const hasSymbols = config?.theme?.generated?.symbols && config.theme.generated.symbols.length > 0;
    
    if (hasSymbols) {
      const symbolsFromStore = [...config.theme.generated.symbols];
      console.log(`Initial load: dispatching ${symbolsFromStore.length} symbols from store`);
      
      // Dispatch symbols immediately
      window.dispatchEvent(new CustomEvent('symbolsChanged', {
        detail: {
          symbols: symbolsFromStore,
          source: 'componentMount'
        }
      }));
    } else {
      // Request symbols if none in store
      window.dispatchEvent(new CustomEvent('requestSymbols'));
    }
    
    // Create a single delayed refresh to ensure everything loads properly
    const refreshTimer = setTimeout(() => {
      // Check store again in case symbols were loaded after initial check
      const { config } = useGameStore.getState();
      if (config?.theme?.generated?.symbols && config.theme.generated.symbols.length > 0) {
        const symbolsFromStore = [...config.theme.generated.symbols];
        window.dispatchEvent(new CustomEvent('symbolsChanged', {
          detail: {
            symbols: symbolsFromStore,
            source: 'delayedRefresh'
          }
        }));
      }
    }, 800);
    
    // Mark as loaded after a short delay
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 300);
    
    return () => {
      resizeObserver.disconnect();
      window.removeEventListener('resize', updateSize);
      clearTimeout(timer);
      clearTimeout(refreshTimer);
    };
  }, [orientation, deviceType]);
  
  // Enforce device-orientation compatibility
  useEffect(() => {
    // Desktop must always be landscape
    if (deviceType === 'desktop' && orientation !== 'landscape') {
      console.log('Desktop can only be in landscape mode. Orientation overridden.');
      setOrientation('landscape');
      
      // Update the store if needed
      if (storeOrientation !== 'landscape') {
        useGameStore.getState().setGridOrientation('landscape');
      }
    }
  }, [deviceType, orientation, storeOrientation]);

  // Handle orientation change
  const handleOrientationChange = () => {
    const newOrientation = orientation === 'landscape' ? 'portrait' : 'landscape';
    
    // Check if we need to enforce landscape for desktop
    if (deviceType === 'desktop' && newOrientation === 'portrait') {
      // Can't change to portrait in desktop mode
      return;
    }
    
    // Get symbols directly from store for reliability
    const { config } = useGameStore.getState();
    const hasSymbols = config?.theme?.generated?.symbols && config.theme.generated.symbols.length > 0;
    let symbolsFromStore: string[] = [];
    
    if (hasSymbols) {
      symbolsFromStore = [...config.theme.generated.symbols];
      console.log('Using store symbols for orientation change, count:', symbolsFromStore.length);
    }
    
    // Update orientation immediately
    setOrientation(newOrientation);
    
    // Update store
    useGameStore.getState().setGridOrientation(newOrientation);
    
    // Set loading without checking symbol presence
    setIsLoading(true);
    
    // Immediate dispatch if we have symbols
    if (hasSymbols) {
      window.dispatchEvent(new CustomEvent('symbolsChanged', {
        detail: {
          symbols: symbolsFromStore,
          source: 'orientationChange'
        }
      }));
      
      // Ultra-short loading state - just enough to show the transition
      setTimeout(() => setIsLoading(false), 80);
    } else {
      // No symbols case - request them once
      window.dispatchEvent(new CustomEvent('requestSymbols'));
      
      // Very short loading state
      setTimeout(() => setIsLoading(false), 50);
    }
  };

  // Handle device type change
  const handleDeviceTypeChange = () => {
    const newDeviceType = deviceType === 'mobile' ? 'desktop' : 'mobile';
    
    // Pre-fetch symbols before changing device type - directly from store for reliability
    let symbolsFromStore: string[] = [];
    
    // Check the store for symbols
    const { config } = useGameStore.getState();
    const hasSymbols = config?.theme?.generated?.symbols && config.theme.generated.symbols.length > 0;
    
    if (hasSymbols) {
      symbolsFromStore = [...config.theme.generated.symbols];
      console.log('Using store symbols for device type change, count:', symbolsFromStore.length);
    }
    
    // First update the device type without showing loading state
    setDeviceType(newDeviceType);
    
    // If switching to desktop, ensure landscape orientation
    if (newDeviceType === 'desktop' && orientation !== 'landscape') {
      setOrientation('landscape');
      useGameStore.getState().setGridOrientation('landscape');
    }
    
    // Use an ultra-short loading flash - just enough for device change
    setIsLoading(true);
    
    // If we have symbols, use them immediately
    if (hasSymbols) {
      // Immediate dispatch symbols - no need to wait
      window.dispatchEvent(new CustomEvent('symbolsChanged', {
        detail: {
          symbols: symbolsFromStore,
          source: 'deviceTypeChange'
        }
      }));
      
      // Hide loading after a very brief delay - just enough for UI to update
      setTimeout(() => setIsLoading(false), 80);
    } else {
      // No symbols case - request them once
      window.dispatchEvent(new CustomEvent('requestSymbols'));
      
      // Ultra-short loading state
      setTimeout(() => setIsLoading(false), 50);
    }
  };

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      // Enter fullscreen
      const container = containerRef.current;
      if (container?.requestFullscreen) {
        container.requestFullscreen()
          .then(() => {
            setIsFullscreen(true);
          })
          .catch(err => {
            console.error('Error attempting to enable fullscreen:', err);
          });
      }
    } else {
      // Exit fullscreen
      if (document.exitFullscreen) {
        document.exitFullscreen()
          .then(() => {
            setIsFullscreen(false);
          })
          .catch(err => {
            console.error('Error attempting to exit fullscreen:', err);
          });
      }
    }
  };

  // Reset grid animation
  const resetAnimation = () => {
    setIsLoading(true);
    
    // Trigger the grid animation reset event
    const event = new Event('resetGridAnimation');
    document.dispatchEvent(event);
    
    // Update loading state after a delay for animation
    setTimeout(() => {
      setIsLoading(false);
    }, 300);
  };
  
  // Refresh grid content - simplified to avoid dependency issues
  const refreshGridContent = () => {
    // Always check store for simplicity and reliability
    const { config } = useGameStore.getState();
    const hasSymbols = config?.theme?.generated?.symbols && config.theme.generated.symbols.length > 0;
    
    // Minimal loading flash
    setIsLoading(true);
    
    // Dispatch symbols if we have them
    if (hasSymbols) {
      // Get symbols from store
      const symbolsFromStore = [...config.theme.generated.symbols];
      console.log(`Refreshing grid content with ${symbolsFromStore.length} symbols from store`);
      
      // Immediately dispatch symbols with no delay
      window.dispatchEvent(new CustomEvent('symbolsChanged', {
        detail: {
          symbols: symbolsFromStore,
          source: 'gridRefresh'
        }
      }));
    } else {
      console.log('No symbols found in store, requesting symbols');
      // No symbols available, request them
      window.dispatchEvent(new CustomEvent('requestSymbols'));
    }
    
    // Always trigger the grid refresh event
    document.dispatchEvent(new Event('refreshGridContent'));
    
    // Minimal loading time - just enough to register visually
    setTimeout(() => setIsLoading(false), 50);
  };

  // Get the correct label based on device type and orientation
  const getDeviceLabel = () => {
    if (deviceType === 'desktop') return 'Desktop';
    return `Mobile – ${orientation === 'landscape' ? 'Landscape' : 'Portrait'}`;
  };
  
  // Get the grid label
  const getGridLabel = () => {
    return `${reels}×${rows} grid – ${getDeviceLabel()} mode`;
  };

  return (
    <div 
      id="grid-preview-container" 
      className={`w-full h-full flex flex-col bg-gray-900 rounded-lg overflow-hidden shadow-xl ${className}`}
      ref={containerRef}
      data-device-type={deviceType}
      data-orientation={orientation}
    >
      {/* Header with Grid Info and Controls */}
      <div className="bg-gradient-to-r from-gray-900 to-gray-800 p-3 rounded-t-lg flex justify-between items-center shadow-lg border-b border-gray-700">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center mr-3 shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" />
            </svg>
          </div>
          <div>
            <h3 className="text-white font-bold text-base">Premium Slot Preview</h3>
            <p id="grid-preview-header" className="text-sm text-blue-300">{getGridLabel()}</p>
          </div>
        </div>
        
        {/* Control Buttons */}
        <div className="flex items-center">
          {/* Check if we're on Step 5 to show preset/advanced toggle */}
          {useGameStore.getState().currentStep === 4 && (
            <div className="flex items-center gap-1 mr-3 bg-gray-800/50 px-2 py-1 rounded-md">
              <span className={`text-xs ${viewMode === 'preset' ? 'font-bold text-blue-400' : 'text-gray-400'}`}>
                Preset
              </span>
              <div 
                className="relative inline-block w-8 h-4 bg-gray-700 rounded-full cursor-pointer"
                onClick={() => {
                  const newMode = viewMode === 'preset' ? 'advanced' : 'preset';
                  setViewMode(newMode);
                  // Dispatch custom event for parent components
                  window.dispatchEvent(new CustomEvent('step5ViewModeChanged', {
                    detail: { mode: newMode }
                  }));
                }}
              >
                <div 
                  className={`absolute w-3.5 h-3.5 rounded-full bg-white shadow-md transform transition-transform duration-300 top-[1px] ${
                    viewMode === 'advanced' ? 'translate-x-4 bg-blue-500' : 'translate-x-0.5'
                  }`}
                ></div>
                <div className={`absolute inset-0 rounded-full transition-colors duration-300 ${
                  viewMode === 'advanced' ? 'bg-blue-600/30' : ''
                }`}></div>
              </div>
              <span className={`text-xs ${viewMode === 'advanced' ? 'font-bold text-blue-400' : 'text-gray-400'}`}>
                Advanced
              </span>
            </div>
          )}
          
          <div className="flex items-center space-x-2">
            {/* Device Type Toggle */}
            <button
              onClick={handleDeviceTypeChange}
              className={`p-2 rounded-md ${deviceType === 'mobile' ? 'bg-blue-600/20 text-blue-400' : 'bg-purple-600/20 text-purple-400'} hover:bg-opacity-30 transition-colors`}
              title={`Switch to ${deviceType === 'mobile' ? 'Desktop' : 'Mobile'} view`}
            >
              {deviceType === 'mobile' ? <Monitor size={18} /> : <Smartphone size={18} />}
            </button>
            
            {/* Orientation Toggle - Disabled for Desktop */}
            <button
              onClick={handleOrientationChange}
              disabled={deviceType === 'desktop'}
              className={`p-2 rounded-md ${
                deviceType === 'desktop' 
                  ? 'bg-gray-700/20 text-gray-500 cursor-not-allowed' 
                  : 'bg-blue-600/20 text-blue-400 hover:bg-opacity-30'
              } transition-colors`}
              title={
                deviceType === 'desktop' 
                  ? 'Desktop is always in landscape mode' 
                  : `Switch to ${orientation === 'landscape' ? 'Portrait' : 'Landscape'} mode`
              }
              aria-label={deviceType === 'desktop' ? 'Orientation toggle (disabled)' : 'Toggle orientation'}
            >
              {/* Rotate icon with conditional styling */}
              <svg 
                className={`w-[18px] h-[18px] transform ${orientation === 'portrait' ? 'rotate-90' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24" 
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect x="2" y="4" width="20" height="16" rx="2" strokeWidth="2" />
              </svg>
            </button>
            
            {/* Zoom Level Toggle */}
            <div className="relative">
              <button
                onClick={() => {
                  // Cycle through zoom levels: auto -> fit -> 100% -> auto
                  const nextZoomLevel: ZoomLevel = 
                    zoomLevel === 'auto' ? 'fit' : 
                    zoomLevel === 'fit' ? '100%' : 'auto';
                  setZoomLevel(nextZoomLevel);
                }}
                className="p-2 rounded-md bg-indigo-600/20 text-indigo-400 hover:bg-opacity-30 transition-colors flex items-center"
                title={`Current zoom: ${getZoomLevelLabel()}, click to change`}
              >
                <svg 
                  className="w-[18px] h-[18px]" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <span className="ml-1 text-xs font-medium">{zoomLevel}</span>
              </button>
            </div>
            
            {/* Reset Animation Button */}
            <button
              onClick={resetAnimation}
              className="p-2 rounded-md bg-green-600/20 text-green-400 hover:bg-opacity-30 transition-colors"
              title="Reset grid animation"
            >
              <RotateCcw size={18} />
            </button>
            
            {/* Refresh Button */}
            <button
              onClick={refreshGridContent}
              className="p-2 rounded-md bg-blue-600/20 text-blue-400 hover:bg-opacity-30 transition-colors"
              title="Refresh grid content"
            >
              <RefreshCw size={18} />
            </button>
          
            {/* Fullscreen Toggle */}
            <button
              onClick={toggleFullscreen}
              className="p-2 rounded-md bg-amber-600/20 text-amber-400 hover:bg-opacity-30 transition-colors"
              title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
            >
              {isFullscreen ? <Minimize size={18} /> : <Maximize size={18} />}
            </button>
          </div>
        </div>
      </div>
      
      {/* Preview Container */}
      <div className="flex-grow flex items-center justify-center bg-[#0a1428] relative overflow-hidden">
        {/* Light Rays Animation */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="light-ray absolute h-[200%] w-[25px] bg-gradient-to-b from-transparent via-blue-400/10 to-transparent rotate-[35deg] -translate-x-full animate-light-sweep"></div>
          <div className="light-ray absolute h-[200%] w-[15px] bg-gradient-to-b from-transparent via-yellow-400/10 to-transparent rotate-[35deg] -translate-x-full animate-light-sweep-delayed"></div>
        </div>
        
        {/* Loading overlay - ultra-fast fade transition */}
        <motion.div 
          className="absolute inset-0 bg-black/20 z-50 flex items-center justify-center pointer-events-none"
          initial={{ opacity: 0 }}
          animate={{ opacity: isLoading ? 1 : 0 }}
          transition={{ duration: 0.08 }} // Ultra-fast transition
        >
          <motion.div 
            className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-400"
            initial={{ scale: 0.8 }}
            animate={{ scale: isLoading ? 1 : 0.8 }}
            transition={{ duration: 0.05 }} // Faster scale transition
          ></motion.div>
        </motion.div>
        
        {/* Main Preview Container - Perfect centering with margin:auto and flexbox */}
        <div 
          className="relative flex items-center justify-center w-full h-full p-4 m-0"
          data-testid="grid-preview-container"
          data-device={deviceType}
          data-orientation={orientation}
        >
          {/* Device Label (outside frame) */}
          <div className="absolute top-2 left-2 z-50 bg-blue-600 text-white text-xs px-2 py-1 rounded shadow-lg">
            {getDeviceLabel()}
          </div>
          
          {/* Device Frame Container - no nested mockups */}
          <motion.div 
            className="preview-container relative mx-auto my-auto flex items-center justify-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            style={{
              maxWidth: '100%',
              maxHeight: '100%'
            }}
          >
            {/* Render appropriate mockup based on device type with direct grid data passing */}
            {deviceType === 'mobile' ? (
              <PhoneMockup 
                orientation={orientation}
                reels={reels}
                rows={rows}
                payMechanism={payMechanism}
                isLoading={isLoading}
                spinButtonSvg={spinButtonSvg}
                spinButtonImageUrl={spinButtonImageUrl}
                balance={balance}
                bet={bet}
                win={win}
                onSpin={onSpin}
                gridScaleModifier={gridScaleModifier}
                verticalOffset={verticalOffset}
                showUnifiedUI={true} // Enable UI components
                framePath={framePath}
                framePosition={framePosition}
                frameScale={frameScale}
                frameStretch={frameStretch}
                backgroundPath={backgroundPath}
                showCellBackgrounds={cellBackgrounds}
              />
            ) : (
              <DesktopMockup
                reels={reels}
                rows={rows}
                payMechanism={payMechanism}
                isLoading={isLoading}
                zoomLevel={zoomLevel}
                spinButtonSvg={spinButtonSvg}
                spinButtonImageUrl={spinButtonImageUrl}
                balance={balance}
                bet={bet}
                win={win}
                onSpin={onSpin}
                gridScaleModifier={gridScaleModifier}
                verticalOffset={verticalOffset}
                mockupDimensions={mockupDimensions}
                framePath={framePath}
                framePosition={framePosition}
                frameScale={frameScale}
                frameStretch={frameStretch}
                backgroundPath={backgroundPath}
                showCellBackgrounds={cellBackgrounds}
              />
            )}
          </motion.div>
        </div>
      </div>
      
      {/* CSS Animations */}
      <style dangerouslySetInnerHTML={{ __html: `
        @keyframes light-sweep {
          0% { transform: translateX(-100%) rotate(35deg); }
          100% { transform: translateX(200%) rotate(35deg); }
        }
        .animate-light-sweep {
          animation: light-sweep 8s infinite ease-in-out;
        }
        .animate-light-sweep-delayed {
          animation: light-sweep 8s infinite 4s ease-in-out;
        }
      `}} />
    </div>
  );
};

export default GridPreviewWrapper;