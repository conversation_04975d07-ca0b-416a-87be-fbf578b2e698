import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useGameStore } from '../../store';
import { gameTypes } from '../animation-lab/components/step-2/gametypes';
import { GameTypeCard } from '../animation-lab/components/step-2/GameTypeCard';

const GameTypeSelector: React.FC = () => {
  const { config, updateConfig, setGameType } = useGameStore();
  const didMount = useRef(false);

  // Local selected state (fallback to classic-reels)
  const [selectedGameTypeId, setSelectedGameTypeId] = useState<string | null>(
    config.selectedGameType || 'classic-reels'
  );

  // 1) Auto‑select Classic Reels on first mount
  useEffect(() => {
    if (!didMount.current) {
      didMount.current = true;
      setTimeout(() => {
        if (!config.selectedGameType || config.selectedGameType !== 'classic-reels') {
          console.log("AUTO-SELECTING Classic Reels on component mount");
          const classicReels = gameTypes.find(type => type.id === 'classic-reels');
          if (classicReels) {
            setSelectedGameTypeId('classic-reels');
            updateConfig({
              ...classicReels.config,
              selectedGameType: 'classic-reels',
              gameTypeInfo: {
                id: 'classic-reels',
                title: classicReels.title,
                description: classicReels.description,
                features: classicReels.highlightFeatures,
                selectedAt: new Date().toISOString()
              }
            });
            setTimeout(() => {
              const card = document.querySelector('[data-game-type="classic-reels"]');
              if (card) {
                card.classList.add('ring-4', 'ring-green-500', 'shadow-lg', 'scale-[1.02]');
              }
            }, 100);
          }
        }
      }, 0);
    }
  }, [config, updateConfig]);

  // 2) Sync local state to store changes
  useEffect(() => {
    if (config.selectedGameType) {
      setSelectedGameTypeId(config.selectedGameType);
      console.log("Setting selected game type from config:", config.selectedGameType);
      setTimeout(() => {
        document.querySelectorAll('[data-game-type]').forEach(card => {
          card.classList.remove('ring-4', 'ring-green-500', 'shadow-lg', 'scale-[1.02]');
        });
        const sel = document.querySelector(`[data-game-type="${config.selectedGameType}"]`);
        if (sel) {
          sel.classList.add('ring-4', 'ring-green-500', 'shadow-lg', 'scale-[1.02]');
          console.log("Applied visual selection to card on mount:", config.selectedGameType);
        }
      }, 100);
    }
  }, [config.selectedGameType]);

  // 3) Ensure there is always a selection (classic-reels fallback)
  useEffect(() => {
    const gameTypeId = config.selectedGameType || 'classic-reels';
    console.log("Component mounted - ensuring selection for:", gameTypeId);
    if (!config.selectedGameType) {
      console.log("No selection in store - forcing selection of classic-reels");
      const selectedType = gameTypes.find(type => type.id === 'classic-reels');
      if (selectedType) {
        setSelectedGameTypeId('classic-reels');
        updateConfig({
          ...selectedType.config,
          selectedGameType: 'classic-reels',
          gameTypeInfo: {
            id: 'classic-reels',
            title: selectedType.title,
            description: selectedType.description,
            features: selectedType.highlightFeatures,
            selectedAt: new Date().toISOString()
          },
          persistSelection: true
        });
        setGameType('slots');
      }
    }
    setTimeout(() => {
      document.querySelectorAll('[data-game-type]').forEach(card => {
        const cardId = card.getAttribute('data-game-type');
        if (cardId === gameTypeId) {
          card.classList.add('ring-4', 'ring-green-500', 'shadow-lg', 'scale-[1.02]');
        } else {
          card.classList.remove('ring-4', 'ring-green-500', 'shadow-lg', 'scale-[1.02]');
        }
      });
    }, 200);
  }, [config.selectedGameType, setGameType, updateConfig]);

  // Selection handler
  const handleSelectGameType = (gameTypeId: string) => {
    if (gameTypeId === selectedGameTypeId && config.selectedGameType === gameTypeId) {
      return;
    }
    const selectedType = gameTypes.find(type => type.id === gameTypeId);
    if (!selectedType) return;

    console.log(`Selected game type: ${gameTypeId}`);
    setSelectedGameTypeId(gameTypeId);

    // immediate visual highlight
    document.querySelectorAll('[data-game-type]').forEach(card => {
      if (card.getAttribute('data-game-type') === gameTypeId) {
        card.classList.add('ring-4', 'ring-green-500', 'shadow-lg', 'scale-[1.02]');
      }
    });

    // update global store
    updateConfig({
      ...selectedType.config,
      selectedGameType: gameTypeId,
      gameTypeInfo: {
        id: gameTypeId,
        title: selectedType.title,
        description: selectedType.description,
        features: selectedType.highlightFeatures,
        selectedAt: new Date().toISOString()
      },
      persistSelection: true
    });

    // re-apply highlights after store update
    setTimeout(() => {
      const current = useGameStore.getState().config.selectedGameType;
      console.log("After updating store, selectedGameType =", current);
      document.querySelectorAll('[data-game-type]').forEach(card => {
        const id = card.getAttribute('data-game-type');
        if (id === current) {
          card.classList.add('ring-4', 'ring-green-500', 'shadow-lg', 'scale-[1.02]');
        } else {
          card.classList.remove('ring-4', 'ring-green-500', 'shadow-lg', 'scale-[1.02]');
        }
      });
    }, 50);

    // notification
    const url = new URL(window.location.href);
    url.searchParams.set('gameType', gameTypeId);
    window.history.pushState({}, '', url.toString());

    const notificationId = 'selection-notification';
    let notification = document.getElementById(notificationId);
    if (!notification) {
      notification = document.createElement('div');
      notification.id = notificationId;
      notification.className =
        'fixed bottom-4 right-4 z-50 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 transform transition-all duration-500 translate-y-20 opacity-0';
      notification.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
        <span>${selectedType.title} selected!</span>
      `;
      document.body.appendChild(notification);
    }
    setTimeout(() => {
      notification!.style.transform = 'translateY(0)';
      notification!.style.opacity = '1';
    }, 100);
    setTimeout(() => {
      notification!.style.transform = 'translateY(20px)';
      notification!.style.opacity = '0';
    }, 1500);
  };

  const isGameTypeAvailable = (gameTypeId: string) =>
    gameTypeId === 'classic-reels';

  // find the currently selected type for the preview pane
  const selectedType = gameTypes.find(t => t.id === selectedGameTypeId) || gameTypes[0];

  return (
    <div className="flex space-x-6 h-screen">
      {/* Left half: your grid */}
      <div className="w-2/3 overflow-y-auto p-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        {gameTypes.map(type => (
          <GameTypeCard
            key={type.id}
            gameType={type}
            isAvailable={isGameTypeAvailable(type.id)}
            onSelect={() => handleSelectGameType(type.id)}
            selectedGameTypeId={selectedGameTypeId!}
          />
        ))}
      </div>

      {/* Right half: live preview */}
      <div className="w-1/3 p-4 bg-gray-50 rounded-lg shadow-inner flex flex-col items-center">
        <h2 className="text-xl font-semibold mb-4">{selectedType.title}</h2>
        <img
          src={selectedType.placeholder}
          alt={`${selectedType.title} placeholder`}
          className="w-full rounded-md shadow-md mt-4"
        />
        <p className="mt-3 text-gray-600">{selectedType.description}</p>
      </div>
    </div>
  );
};

export default GameTypeSelector;
