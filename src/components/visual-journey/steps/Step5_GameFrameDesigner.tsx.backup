import React, { useState, useEffect, useRef } from 'react';
import { useGameStore } from '../../../store';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Frame,
  Paintbrush,
  Wand2,
  RefreshCw,
  CheckCircle,
  Loader,
  Sparkles,
  ImageIcon,
  Palette,
  Settings,
  Smartphone,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { detectThemeCategory, getMockupAsset } from '../../../utils/mockupService';
import { useKeenSlider } from 'keen-slider/react';
import "keen-slider/keen-slider.min.css";

// Define types for frame generation
interface FrameConfig {
  path: string | null;
  style: FrameStyle;
  material: FrameMaterial;
  decoration: FrameDecoration;
  isGenerating?: boolean;
  progress?: number;
}

type FrameStyle = 
  | 'Same as Symbols' 
  | 'Cartoon' 
  | 'Realistic' 
  | 'Cute' 
  | 'Dark' 
  | 'Neon' 
  | 'Futuristic' 
  | 'Minimal';

type FrameMaterial = 
  | 'Metallic' 
  | 'Wood' 
  | 'Glass' 
  | 'Soft' 
  | 'Organic' 
  | 'Neon';

type FrameDecoration = 'Minimal' | 'Decorated';

// Style options with descriptions
const STYLE_OPTIONS: Array<{id: FrameStyle, name: string, description: string}> = [
  {
    id: 'Same as Symbols',
    name: 'Same as Symbols',
    description: 'Match the visual style of your generated symbols'
  },
  {
    id: 'Cartoon',
    name: 'Cartoon',
    description: 'Bold outlines and vibrant colors with a playful feel'
  },
  {
    id: 'Realistic',
    name: 'Realistic',
    description: 'Photorealistic details and textures'
  },
  {
    id: 'Cute',
    name: 'Cute',
    description: 'Adorable, rounded designs with friendly elements'
  },
  {
    id: 'Dark',
    name: 'Dark',
    description: 'Dramatic, mysterious aesthetics with high contrast'
  },
  {
    id: 'Neon',
    name: 'Neon',
    description: 'Glowing elements against dark backgrounds'
  },
  {
    id: 'Futuristic',
    name: 'Futuristic',
    description: 'Hi-tech, sleek designs with digital elements'
  },
  {
    id: 'Minimal',
    name: 'Minimal',
    description: 'Clean, simple designs with essential elements only'
  }
];

// Material options with descriptions
const MATERIAL_OPTIONS: Array<{id: FrameMaterial, name: string, description: string}> = [
  {
    id: 'Metallic',
    name: 'Metallic',
    description: 'Shiny, reflective surfaces with metallic textures'
  },
  {
    id: 'Wood',
    name: 'Wood',
    description: 'Natural wood grain textures and patterns'
  },
  {
    id: 'Glass',
    name: 'Glass',
    description: 'Transparent or translucent with reflections'
  },
  {
    id: 'Soft',
    name: 'Soft',
    description: 'Plush, fabric-like textures with a tactile feel'
  },
  {
    id: 'Organic',
    name: 'Organic',
    description: 'Natural elements like leaves, vines, or coral'
  },
  {
    id: 'Neon',
    name: 'Neon',
    description: 'Glowing, light-emitting materials'
  }
];

// Decoration level options
const DECORATION_OPTIONS: Array<{id: FrameDecoration, name: string, description: string}> = [
  {
    id: 'Minimal',
    name: 'Minimal',
    description: 'Clean and simple design with minimal ornamental elements'
  },
  {
    id: 'Decorated',
    name: 'Decorated',
    description: 'Ornate design with detailed decorative elements'
  }
];

/**
 * Game Frame Designer Component
 */
const Step5_GameFrameDesigner: React.FC = () => {
  const { config, updateConfig } = useGameStore();
  
  // Get the symbol style from previous step if available
  const symbolStyle = config?.theme?.artStyle || 'cartoon';
  
  // Initialize frame configuration
  const [frameConfig, setFrameConfig] = useState<FrameConfig>({
    path: config?.theme?.generated?.frame || null,
    style: config?.frame?.style || 'Same as Symbols',
    material: config?.frame?.material || 'Metallic',
    decoration: config?.frame?.decoration || 'Minimal',
    isGenerating: false,
    progress: 0
  });
  
  // State for previews
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [notification, setNotification] = useState<{message: string, type: 'success' | 'error' | 'info'} | null>(null);
  const [showSymbols, setShowSymbols] = useState(false);
  
  // Get theme from configuration
  const theme = config?.theme?.mainTheme || 'fantasy slot game';
  const themeCategory = detectThemeCategory(theme);
  
  // Get symbol paths for preview
  const symbolPaths = {
    wild: `/assets/mockups/${themeCategory}/symbols/wild.png`,
    scatter: `/assets/mockups/${themeCategory}/symbols/scatter.png`,
    high_1: `/assets/mockups/${themeCategory}/symbols/high_1.png`,
    high_2: `/assets/mockups/${themeCategory}/symbols/high_2.png`,
    high_3: `/assets/mockups/${themeCategory}/symbols/high_3.png`,
    mid_1: `/assets/mockups/${themeCategory}/symbols/mid_1.png`,
    mid_2: `/assets/mockups/${themeCategory}/symbols/mid_2.png`,
    low_1: `/assets/mockups/${themeCategory}/symbols/low_1.png`,
    low_2: `/assets/mockups/${themeCategory}/symbols/low_2.png`,
    low_3: `/assets/mockups/${themeCategory}/symbols/low_3.png`
  };
  
  // Effect to update frame config when symbols style changes
  useEffect(() => {
    if (frameConfig.style === 'Same as Symbols') {
      // Map the symbol art style to a frame style
      let mappedStyle: FrameStyle = 'Cartoon';
      
      switch (symbolStyle.toLowerCase()) {
        case 'cartoon':
          mappedStyle = 'Cartoon';
          break;
        case 'realistic':
          mappedStyle = 'Realistic';
          break;
        case 'cute':
          mappedStyle = 'Cute';
          break;
        case 'dark':
          mappedStyle = 'Dark';
          break;
        case 'neon':
          mappedStyle = 'Neon';
          break;
        case 'futuristic':
          mappedStyle = 'Futuristic';
          break;
        case 'minimal':
          mappedStyle = 'Minimal';
          break;
        default:
          mappedStyle = 'Cartoon';
      }
      
      updateConfig({
        frame: {
          ...config.frame,
          style: mappedStyle,
          material: frameConfig.material,
          decoration: frameConfig.decoration
        }
      });
    }
  }, [symbolStyle, frameConfig.style]);
  
  // Update frame config options
  const updateFrameOption = (
    option: 'style' | 'material' | 'decoration',
    value: FrameStyle | FrameMaterial | FrameDecoration
  ) => {
    const newConfig = { ...frameConfig, [option]: value };
    setFrameConfig(newConfig);
    
    // Also update the main config
    updateConfig({
      frame: {
        ...config.frame,
        [option]: value
      }
    });
  };
  
  // Generate frame based on current settings
  const generateFrame = () => {
    if (isGenerating) return;
    
    setIsGenerating(true);
    setGenerationProgress(0);
    setShowSymbols(false);
    
    // Get the theme category for mockup selection
    const themeCategory = detectThemeCategory(theme);
    
    // Simulate generation progress (in a real implementation, this would be an API call)
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 5 + 2; // Random progress increment
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        
        // Mark as complete after a slight delay
        setTimeout(() => {
          completeGeneration(themeCategory);
        }, 500);
      }
      
      setGenerationProgress(Math.min(100, Math.round(progress)));
    }, 200);
  };
  
  // Complete frame generation
  const completeGeneration = (themeCategory: string) => {
    // Get the mockup frame image based on theme category
    const framePath = getMockupAsset('frame', themeCategory);
    
    // Update local state
    setFrameConfig(prev => ({
      ...prev,
      path: framePath
    }));
    
    // Update the main config with the generated frame
    updateConfig({
      theme: {
        ...config.theme,
        generated: {
          ...config.theme?.generated,
          frame: framePath
        }
      },
      frame: {
        ...config.frame,
        style: frameConfig.style === 'Same as Symbols' 
          ? convertSymbolStyleToFrameStyle(symbolStyle) 
          : frameConfig.style,
        material: frameConfig.material,
        decoration: frameConfig.decoration
      }
    });
    
    // Reset generation state and show notification
    setIsGenerating(false);
    setShowSymbols(true);
    setNotification({
      message: 'Frame generated successfully!',
      type: 'success'
    });
    setTimeout(() => setNotification(null), 5000);
  };
  
  // Convert symbol style to frame style
  const convertSymbolStyleToFrameStyle = (style: string): FrameStyle => {
    switch (style.toLowerCase()) {
      case 'cartoon':
        return 'Cartoon';
      case 'realistic':
        return 'Realistic';
      case 'cute':
        return 'Cute';
      case 'dark':
        return 'Dark';
      case 'neon':
        return 'Neon';
      case 'futuristic':
        return 'Futuristic';
      case 'minimal':
        return 'Minimal';
      default:
        return 'Cartoon';
    }
  };
  
  // Get actual style name (for display when using 'Same as Symbols')
  const getActualStyleName = (): string => {
    if (frameConfig.style === 'Same as Symbols') {
      return `${convertSymbolStyleToFrameStyle(symbolStyle)} (from symbols)`;
    }
    return frameConfig.style;
  };
  
  // Toggle symbols visibility
  const toggleSymbols = () => {
    setShowSymbols(!showSymbols);
  };
  
  return (
    <div className="step-container max-w-5xl mx-auto">
      <h2 className="text-2xl font-bold mb-2 text-center">Game Frame Designer</h2>
      <p className="text-center text-gray-600 mb-6 max-w-2xl mx-auto">
        Design a custom frame that surrounds your slot reels and defines your game's visual identity.
      </p>
      
      {/* Theme & Generation Controls */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
        <div className="p-5 border-b border-gray-100">
          <div className="flex flex-col md:flex-row justify-between gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">Game Theme</label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={theme}
                  readOnly
                  className="w-full bg-gray-50 border border-gray-200 rounded-lg px-4 py-2 text-gray-500"
                  placeholder="Set in Theme step"
                />
                <button 
                  className="bg-gray-100 p-2 rounded-lg text-gray-500 hover:bg-gray-200 hover:text-gray-700"
                  title="Theme is set in the first step"
                >
                  <Paintbrush className="w-5 h-5" />
                </button>
              </div>
              <p className="mt-1 text-xs text-gray-500">Theme is set in Step 1 and affects frame generation</p>
            </div>
            
            <div className="flex items-center gap-2">
              <button
                onClick={generateFrame}
                disabled={isGenerating}
                className={`h-10 flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium ${
                  isGenerating 
                    ? 'bg-gray-100 text-gray-400 cursor-wait' 
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {isGenerating ? (
                  <>
                    <Loader className="w-4 h-4 animate-spin" />
                    Generating...
                  </>
                ) : frameConfig.path ? (
                  <>
                    <RefreshCw className="w-4 h-4" />
                    Regenerate Frame
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4" />
                    Generate Frame
                  </>
                )}
              </button>
              
              <button 
                className="h-10 w-10 flex items-center justify-center rounded-lg bg-gray-100 text-gray-500 hover:bg-gray-200 hover:text-gray-700"
                title="Frame Configuration"
                onClick={toggleSymbols}
              >
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
        
        {/* Notification */}
        {notification && (
          <div className={`m-5 p-3 rounded-lg text-sm flex items-start gap-2 ${
            notification.type === 'success' ? 'bg-green-50 border border-green-100 text-green-800' :
            notification.type === 'error' ? 'bg-red-50 border border-red-100 text-red-800' :
            'bg-blue-50 border border-blue-100 text-blue-800'
          }`}>
            {notification.type === 'success' ? (
              <CheckCircle className="w-5 h-5 flex-shrink-0" />
            ) : notification.type === 'error' ? (
              <CheckCircle className="w-5 h-5 flex-shrink-0" />
            ) : (
              <ImageIcon className="w-5 h-5 flex-shrink-0" />
            )}
            <span>{notification.message}</span>
          </div>
        )}
      </div>
      
      {/* Frame Configuration Options - Horizontal Carousels */}
      <div className="mb-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-5 border-b border-gray-100">
            <h3 className="font-semibold text-gray-800 flex items-center gap-2">
              <Paintbrush className="text-blue-600 w-5 h-5" />
              Frame Customization
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              Select options to customize your frame's appearance
            </p>
          </div>
          
          <div className="p-5">
            {/* Compact Carousels Container - All side by side */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Visual Style Carousel - Keen Slider */}
              <div className="compact-carousel">
                <div className="flex items-center gap-1.5 mb-2">
                  <Paintbrush className="text-blue-600 w-4 h-4" />
                  <h4 className="font-medium text-gray-800 text-sm">Style: <span className="text-blue-600">{frameConfig.style}</span></h4>
                </div>
                
                <StyleCarousel 
                  options={STYLE_OPTIONS} 
                  selected={frameConfig.style}
                  onChange={(style) => updateFrameOption('style', style)}
                  color="blue"
                />
                
                <p className="text-xs text-gray-500 mt-1 text-center">
                  {/* Image path info for styles */}
                  <span className="font-mono bg-gray-100 px-1 rounded">public/assets/frames/styles/{frameConfig.style.toLowerCase().replace(/\s+/g, '-')}.png (300x400)</span>
                </p>
              </div>
              
              {/* Material Carousel - Keen Slider */}
              <div className="compact-carousel">
                <div className="flex items-center gap-1.5 mb-2">
                  <Palette className="text-purple-600 w-4 h-4" />
                  <h4 className="font-medium text-gray-800 text-sm">Material: <span className="text-purple-600">{frameConfig.material}</span></h4>
                </div>
                
                <MaterialCarousel 
                  options={MATERIAL_OPTIONS} 
                  selected={frameConfig.material}
                  onChange={(material) => updateFrameOption('material', material)}
                  color="purple"
                />
                
                <p className="text-xs text-gray-500 mt-1 text-center">
                  {/* Image path info for materials */}
                  <span className="font-mono bg-gray-100 px-1 rounded">public/assets/frames/materials/{frameConfig.material.toLowerCase()}.png (300x400)</span>
                </p>
              </div>
              
              {/* Decoration Level - Stacked Thumbnails */}
              <div className="compact-carousel">
                <div className="flex items-center gap-1.5 mb-2">
                  <Wand2 className="text-green-600 w-4 h-4" />
                  <h4 className="font-medium text-gray-800 text-sm">Decoration: <span className="text-green-600">{frameConfig.decoration}</span></h4>
                </div>
                
                <div className="flex flex-col gap-2 max-w-[200px] mx-auto">
                  {/* Minimal Option */}
                  <div 
                    className={`h-[200px] w-[200px] rounded-lg cursor-pointer transition-all overflow-hidden ${
                      frameConfig.decoration === 'Minimal'
                        ? 'ring-2 ring-green-500 shadow-md'
                        : 'ring-1 ring-gray-200 opacity-75 hover:opacity-100'
                    }`}
                    onClick={() => updateFrameOption('decoration', 'Minimal')}
                  >
                    <div className="w-full h-full bg-gradient-to-br from-gray-50 to-gray-100 flex flex-col items-center justify-center p-3">
                      <span className="font-medium text-gray-800 mb-2">Minimal</span>
                      <span className="text-sm text-center text-gray-600">
                        Clean design with minimal elements
                      </span>
                      {frameConfig.decoration === 'Minimal' && (
                        <div className="mt-3 bg-green-500 text-white rounded-full p-1 shadow-md">
                          <CheckCircle className="w-4 h-4" />
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Decorated Option */}
                  <div 
                    className={`h-[200px] w-[200px] rounded-lg cursor-pointer transition-all overflow-hidden ${
                      frameConfig.decoration === 'Decorated'
                        ? 'ring-2 ring-green-500 shadow-md'
                        : 'ring-1 ring-gray-200 opacity-75 hover:opacity-100'
                    }`}
                    onClick={() => updateFrameOption('decoration', 'Decorated')}
                  >
                    <div className="w-full h-full bg-gradient-to-br from-amber-50 to-amber-200 flex flex-col items-center justify-center p-3">
                      <span className="font-medium text-gray-800 mb-2">Decorated</span>
                      <span className="text-sm text-center text-gray-600">
                        Ornate with decorative elements
                      </span>
                      {frameConfig.decoration === 'Decorated' && (
                        <div className="mt-3 bg-green-500 text-white rounded-full p-1 shadow-md">
                          <CheckCircle className="w-4 h-4" />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                <p className="text-xs text-gray-500 mt-1 text-center">
                  {/* Image path info for decoration */}
                  <span className="font-mono bg-gray-100 px-1 rounded">public/assets/frames/decorations/{frameConfig.decoration.toLowerCase()}.png (200x200)</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Generation Controls */}
      <div className="mb-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-5 border-b border-gray-100">
            <h3 className="font-semibold text-gray-800 flex items-center gap-2">
              <Sparkles className="text-blue-600 w-5 h-5" />
              Generate Frame
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              Generate your frame based on the customization options selected above
            </p>
          </div>
          
          <div className="p-5 flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex-1">
              <div className="flex gap-2 items-center">
                <label className="text-sm font-medium text-gray-700">Theme:</label>
                <span className="text-sm font-medium text-blue-600">{theme}</span>
              </div>
              <div className="flex gap-2 mt-2">
                <span className="text-xs bg-blue-50 text-blue-700 rounded-full px-2 py-0.5">Style: {getActualStyleName()}</span>
                <span className="text-xs bg-purple-50 text-purple-700 rounded-full px-2 py-0.5">Material: {frameConfig.material}</span>
                <span className="text-xs bg-green-50 text-green-700 rounded-full px-2 py-0.5">Decoration: {frameConfig.decoration}</span>
              </div>
            </div>
            
            <div className="flex gap-3">
              <button
                onClick={generateFrame}
                disabled={isGenerating}
                className={`flex items-center gap-2 px-5 py-2.5 rounded-lg text-sm font-medium ${
                  isGenerating 
                    ? 'bg-gray-100 text-gray-400 cursor-wait' 
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {isGenerating ? (
                  <>
                    <Loader className="w-4 h-4 animate-spin" />
                    Generating...
                  </>
                ) : frameConfig.path ? (
                  <>
                    <RefreshCw className="w-4 h-4" />
                    Regenerate Frame
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4" />
                    Generate Frame
                  </>
                )}
              </button>
              
              <button 
                className="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 text-gray-500 hover:bg-gray-200 hover:text-gray-700"
                title="Toggle Symbols Preview"
                onClick={toggleSymbols}
              >
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Frame Preview */}
      <div className="mb-8">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-5 border-b border-gray-100">
            <h3 className="font-semibold text-gray-800 flex items-center gap-2">
              <Frame className="text-blue-600 w-5 h-5" />
              Frame Preview
            </h3>
          </div>
          
          <div className="p-5 flex flex-col items-center">
            <div className="mb-6 relative w-full max-w-lg aspect-[16/9] bg-gray-50 rounded-lg overflow-hidden border border-gray-200">
              {/* Generation Overlay */}
              {isGenerating && (
                <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-900 bg-opacity-70 z-10">
                  <Loader className="w-10 h-10 text-white animate-spin mb-4" />
                  <div className="w-1/2 h-2 bg-gray-700 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-white transition-all duration-300 ease-out"
                      style={{ width: `${generationProgress}%` }}
                    />
                  </div>
                  <p className="text-white text-sm mt-3">{generationProgress}%</p>
                </div>
              )}
              
              {/* Frame Image */}
              {frameConfig.path ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                  className="w-full h-full relative"
                >
                  <img 
                    src={frameConfig.path} 
                    alt="Game Frame" 
                    className="w-full h-full object-contain"
                  />
                  
                  {/* Overlay symbols in a grid - reusing from original implementation */}
                  {showSymbols && (
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[62%] h-[55%] grid grid-cols-5 grid-rows-3 gap-[2px]">
                      {/* Row 1 */}
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.high_1} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.high_2} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.wild} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.high_3} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.mid_1} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      
                      {/* Row 2 */}
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.mid_2} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.scatter} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.high_1} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.low_1} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.high_2} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      
                      {/* Row 3 */}
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.low_2} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.mid_1} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.low_3} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.wild} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                      <div className="flex items-center justify-center p-1">
                        <img src={symbolPaths.scatter} alt="Symbol" className="w-full h-full object-contain max-w-[100%] max-h-[100%]" />
                      </div>
                    </div>
                  )}
                </motion.div>
              ) : (
                <div className="w-full h-full flex flex-col items-center justify-center text-gray-400">
                  <Frame className="w-16 h-16 mb-2" />
                  <p className="text-center text-gray-500">
                    Your generated frame will appear here.
                    <br />
                    <span className="text-sm text-gray-400">
                      Configure options above and click Generate
                    </span>
                  </p>
                </div>
              )}
            </div>
            
            {/* Current Config Summary */}
            {frameConfig.path && (
              <div className="w-full max-w-md p-3 bg-gray-50 rounded-lg border border-gray-100 text-sm text-gray-600">
                <div className="flex flex-wrap gap-2 justify-center">
                  <span className="px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-xs flex items-center gap-1">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    Style: {getActualStyleName()}
                  </span>
                  <span className="px-2 py-1 bg-purple-50 text-purple-700 rounded-full text-xs flex items-center gap-1">
                    <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                    Material: {frameConfig.material}
                  </span>
                  <span className="px-2 py-1 bg-green-50 text-green-700 rounded-full text-xs flex items-center gap-1">
                    <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                    Decoration: {frameConfig.decoration}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Custom CSS for carousel components */}
      <style jsx>{`
        .carousel-container {
          position: relative;
          overflow: hidden;
        }
        
        .carousel-viewport {
          width: 100%;
          overflow: hidden;
        }
        
        .carousel-track {
          display: flex;
          transition: transform 0.5s ease-out;
        }
        
        .carousel-slide {
          width: 100%;
          flex-shrink: 0;
          position: relative;
        }
        
        .poster-thumbnail {
          position: relative;
          width: 100%;
          height: 100%;
          transition: all 0.3s ease-out;
        }
        
        .poster-thumbnail:hover {
          transform: scale(1.02);
        }
      `}</style>
      
      {/* Mobile Optimization Tips */}
      <div className="mt-8 bg-blue-50 border border-blue-100 rounded-lg p-4 flex items-start gap-3">
        <div className="text-blue-600 flex-shrink-0 mt-0.5">
          <Smartphone className="w-5 h-5" />
        </div>
        <div>
          <h4 className="font-medium text-blue-800">Mobile Optimization Tips</h4>
          <p className="text-sm text-blue-700 mt-1">
            Frames should look good on both portrait and landscape orientations. Consider using 
            less detail for mobile screens to ensure visibility of important elements. The frame 
            should enhance your game without distracting from the main gameplay elements.
          </p>
        </div>
      </div>
    </div>
  );
};

// Carousel Components
interface CarouselProps<T extends string> {
  options: Array<{id: T, name: string, description: string}>;
  selected: T;
  onChange: (value: T) => void;
  color: 'blue' | 'purple' | 'green';
}

// Style Carousel Component
const StyleCarousel = ({ options, selected, onChange, color }: CarouselProps<FrameStyle>) => {
  const selectedIndex = options.findIndex(option => option.id === selected);
  
  const [sliderRef, instanceRef] = useKeenSlider({
    initial: selectedIndex,
    loop: true,
    mode: "snap",
    slides: {
      origin: "center",
      perView: 1.5,
      spacing: 15,
    },
    slideChanged(slider) {
      const currentSlide = slider.track.details.rel;
      onChange(options[currentSlide].id);
    },
  });

  const getBgColor = (style: FrameStyle) => {
    switch(style) {
      case 'Cartoon': return 'bg-gradient-to-br from-pink-100 to-purple-100';
      case 'Realistic': return 'bg-gradient-to-br from-slate-100 to-slate-300';
      case 'Cute': return 'bg-gradient-to-br from-pink-100 to-rose-200';
      case 'Dark': return 'bg-gradient-to-br from-gray-700 to-gray-900';
      case 'Neon': return 'bg-gradient-to-br from-purple-900 to-indigo-900';
      case 'Futuristic': return 'bg-gradient-to-br from-blue-100 to-cyan-200';
      case 'Minimal': return 'bg-gradient-to-br from-gray-50 to-gray-200';
      default: return 'bg-gradient-to-br from-blue-50 to-blue-100';
    }
  };

  const getTextColor = (style: FrameStyle) => {
    return style === 'Dark' || style === 'Neon' ? 'text-white' : 'text-gray-800';
  };

  const getSubTextColor = (style: FrameStyle) => {
    return style === 'Dark' || style === 'Neon' ? 'text-gray-300' : 'text-gray-600';
  };

  return (
    <div className="relative keen-slider-container h-[400px]">
      <button
        onClick={() => instanceRef.current?.prev()}
        className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white/80 rounded-full p-1.5 shadow-md z-10 text-gray-500 hover:text-gray-700"
      >
        <ChevronLeft className="w-5 h-5" />
      </button>
      
      <div ref={sliderRef} className="keen-slider h-full">
        {options.map((style) => (
          <div key={style.id} className="keen-slider__slide">
            <div 
              className={`poster-thumbnail rounded-lg cursor-pointer transition-all overflow-hidden h-full aspect-[3/4] mx-auto ${
                selected === style.id
                  ? `ring-3 ring-${color}-500 shadow-lg scale-100`
                  : 'ring-1 ring-gray-200 scale-95 opacity-70'
              }`}
              onClick={() => onChange(style.id)}
            >
              <div className={`w-full h-full flex flex-col items-center justify-center p-4 ${getBgColor(style.id)}`}>
                <span className={`font-medium mb-2 ${getTextColor(style.id)}`}>
                  {style.name}
                </span>
                <span className={`text-xs text-center ${getSubTextColor(style.id)}`}>
                  {style.description}
                </span>
                {selected === style.id && (
                  <div className={`mt-4 bg-${color}-500 text-white rounded-full p-1 shadow-md`}>
                    <CheckCircle className="w-4 h-4" />
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <button
        onClick={() => instanceRef.current?.next()}
        className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white/80 rounded-full p-1.5 shadow-md z-10 text-gray-500 hover:text-gray-700"
      >
        <ChevronRight className="w-5 h-5" />
      </button>
    </div>
  );
};

// Material Carousel Component
const MaterialCarousel = ({ options, selected, onChange, color }: CarouselProps<FrameMaterial>) => {
  const selectedIndex = options.findIndex(option => option.id === selected);
  
  const [sliderRef, instanceRef] = useKeenSlider({
    initial: selectedIndex,
    loop: true,
    mode: "snap",
    slides: {
      origin: "center",
      perView: 1.5,
      spacing: 15,
    },
    slideChanged(slider) {
      const currentSlide = slider.track.details.rel;
      onChange(options[currentSlide].id);
    },
  });

  const getBgColor = (material: FrameMaterial) => {
    switch(material) {
      case 'Metallic': return 'bg-gradient-to-br from-gray-300 to-gray-400';
      case 'Wood': return 'bg-gradient-to-br from-amber-100 to-amber-300';
      case 'Glass': return 'bg-gradient-to-br from-blue-50 to-blue-100';
      case 'Soft': return 'bg-gradient-to-br from-pink-50 to-pink-100';
      case 'Organic': return 'bg-gradient-to-br from-green-100 to-green-200';
      case 'Neon': return 'bg-gradient-to-br from-purple-400 to-purple-600';
      default: return 'bg-gradient-to-br from-gray-100 to-gray-200';
    }
  };

  const getTextColor = (material: FrameMaterial) => {
    return material === 'Neon' ? 'text-white' : 'text-gray-800';
  };

  const getSubTextColor = (material: FrameMaterial) => {
    return material === 'Neon' ? 'text-gray-100' : 'text-gray-600';
  };

  return (
    <div className="relative keen-slider-container h-[400px]">
      <button
        onClick={() => instanceRef.current?.prev()}
        className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white/80 rounded-full p-1.5 shadow-md z-10 text-gray-500 hover:text-gray-700"
      >
        <ChevronLeft className="w-5 h-5" />
      </button>
      
      <div ref={sliderRef} className="keen-slider h-full">
        {options.map((material) => (
          <div key={material.id} className="keen-slider__slide">
            <div 
              className={`poster-thumbnail rounded-lg cursor-pointer transition-all overflow-hidden h-full aspect-[3/4] mx-auto ${
                selected === material.id
                  ? `ring-3 ring-${color}-500 shadow-lg scale-100`
                  : 'ring-1 ring-gray-200 scale-95 opacity-70'
              }`}
              onClick={() => onChange(material.id)}
            >
              <div className={`w-full h-full flex flex-col items-center justify-center p-4 ${getBgColor(material.id)}`}>
                <span className={`font-medium mb-2 ${getTextColor(material.id)}`}>
                  {material.name}
                </span>
                <span className={`text-xs text-center ${getSubTextColor(material.id)}`}>
                  {material.description}
                </span>
                {selected === material.id && (
                  <div className={`mt-4 bg-${color}-500 text-white rounded-full p-1 shadow-md`}>
                    <CheckCircle className="w-4 h-4" />
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <button
        onClick={() => instanceRef.current?.next()}
        className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white/80 rounded-full p-1.5 shadow-md z-10 text-gray-500 hover:text-gray-700"
      >
        <ChevronRight className="w-5 h-5" />
      </button>
    </div>
  );
};

// Decoration Carousel Component
const DecorationCarousel = ({ options, selected, onChange, color }: CarouselProps<FrameDecoration>) => {
  const selectedIndex = options.findIndex(option => option.id === selected);
  
  const [sliderRef, instanceRef] = useKeenSlider({
    initial: selectedIndex,
    loop: true,
    mode: "snap",
    slides: {
      origin: "center",
      perView: 1.5,
      spacing: 15,
    },
    slideChanged(slider) {
      const currentSlide = slider.track.details.rel;
      onChange(options[currentSlide].id);
    },
  });

  const getBgColor = (decoration: FrameDecoration) => {
    switch(decoration) {
      case 'Minimal': return 'bg-gradient-to-br from-gray-50 to-gray-100';
      case 'Decorated': return 'bg-gradient-to-br from-amber-50 to-amber-200';
      default: return 'bg-gradient-to-br from-gray-100 to-gray-200';
    }
  };

  return (
    <div className="relative keen-slider-container h-[400px]">
      <button
        onClick={() => instanceRef.current?.prev()}
        className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white/80 rounded-full p-1.5 shadow-md z-10 text-gray-500 hover:text-gray-700"
      >
        <ChevronLeft className="w-5 h-5" />
      </button>
      
      <div ref={sliderRef} className="keen-slider h-full">
        {options.map((decoration) => (
          <div key={decoration.id} className="keen-slider__slide">
            <div 
              className={`poster-thumbnail rounded-lg cursor-pointer transition-all overflow-hidden h-full aspect-[3/4] mx-auto ${
                selected === decoration.id
                  ? `ring-3 ring-${color}-500 shadow-lg scale-100`
                  : 'ring-1 ring-gray-200 scale-95 opacity-70'
              }`}
              onClick={() => onChange(decoration.id)}
            >
              <div className={`w-full h-full flex flex-col items-center justify-center p-4 ${getBgColor(decoration.id)}`}>
                <span className="font-medium mb-2 text-gray-800">
                  {decoration.name}
                </span>
                <span className="text-xs text-center text-gray-600">
                  {decoration.description}
                </span>
                {selected === decoration.id && (
                  <div className={`mt-4 bg-${color}-500 text-white rounded-full p-1 shadow-md`}>
                    <CheckCircle className="w-4 h-4" />
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <button
        onClick={() => instanceRef.current?.next()}
        className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white/80 rounded-full p-1.5 shadow-md z-10 text-gray-500 hover:text-gray-700"
      >
        <ChevronRight className="w-5 h-5" />
      </button>
    </div>
  );
};

export default Step5_GameFrameDesigner;