export const themeOptions = [
  {
    id: 'ancient-egypt',
    name: 'Ancient Egypt',
    description: 'Explore the treasures of pharaohs and pyramids',
    colors: {
      primary: '#D4AF37',
      secondary: '#1E3F66',
      accent: '#BA0020',
      background: '#F2D2A9'
    },
    keywords: ['pyramids', 'pharaoh', 'hieroglyphs', 'sphinx', 'gold', 'desert'],
    symbolIdeas: ['scarab', 'ankh', 'eye of horus', 'mummy', 'pyramid', 'pharaoh mask'],
    previewImage: '/themes/ancient-egypt.avif',
    moodImage: 'https://placehold.co/600x400/F2D2A9/000000/png?text=Ancient+Egypt'
  },
  {
    id: 'cosmic-adventure',
    name: 'Cosmic Adventure',
    description: 'Journey through space with galaxies and nebulae',
    colors: {
      primary: '#6A0DAD',
      secondary: '#00BFFF',
      accent: '#FF4500',
      background: '#0A0A2A'
    },
    keywords: ['space', 'planets', 'galaxies', 'astronauts', 'stars', 'cosmic'],
    symbolIdeas: ['planet', 'rocket', 'astronaut', 'alien', 'ufo', 'star'],
    previewImage: '/themes/cosmic-adventure.avif',
    moodImage: 'https://placehold.co/600x400/0A0A2A/FFFFFF/png?text=Cosmic+Adventure'
  },
  {
    id: 'enchanted-forest',
    name: 'Enchanted Forest',
    description: 'Magical woodland realm with mystical creatures',
    colors: {
      primary: '#228B22',
      secondary: '#9932CC',
      accent: '#FFD700',
      background: '#1A472A'
    },
    keywords: ['magic', 'woodland', 'fairies', 'elves', 'mystical', 'nature'],
    symbolIdeas: ['fairy', 'mushroom', 'tree spirit', 'magic potion', 'unicorn', 'crystal'],
    previewImage: '/themes/enchanted-forest.avif',
    moodImage: 'https://placehold.co/600x400/1A472A/FFFFFF/png?text=Enchanted+Forest'
  },
  {
    id: 'deep-ocean',
    name: 'Deep Ocean',
    description: 'Explore the mysterious depths of the sea',
    colors: {
      primary: '#00008B',
      secondary: '#20B2AA',
      accent: '#FF7F50',
      background: '#000080'
    },
    keywords: ['underwater', 'sea creatures', 'treasure', 'shipwreck', 'coral', 'ocean'],
    symbolIdeas: ['mermaid', 'treasure chest', 'anchor', 'whale', 'jellyfish', 'coral'],
    previewImage: '/themes/deep-ocean.avif',
    moodImage: 'https://placehold.co/600x400/000080/FFFFFF/png?text=Deep+Ocean'
  },
  {
    id: 'wild-west',
    name: 'Wild West',
    description: 'Frontier adventure in the old American West',
    colors: {
      primary: '#8B4513',
      secondary: '#DAA520',
      accent: '#DC143C',
      background: '#F4A460'
    },
    keywords: ['cowboys', 'sheriff', 'western', 'gold rush', 'desert', 'saloon'],
    symbolIdeas: ['sheriff badge', 'horseshoe', 'revolver', 'cowboy hat', 'whiskey', 'gold nugget'],
    previewImage: '/themes/wild-west.avif',
    moodImage: 'https://placehold.co/600x400/F4A460/000000/png?text=Wild+West'
  },
  {
    id: 'asian-dynasty',
    name: 'Asian Dynasty',
    description: 'Ancient Far East with dragons and emperors',
    colors: {
      primary: '#E60000',
      secondary: '#FFD700',
      accent: '#000000',
      background: '#EFE1C6'
    },
    keywords: ['dynasty', 'dragon', 'emperor', 'temple', 'cherry blossom', 'lantern'],
    symbolIdeas: ['dragon', 'fan', 'lantern', 'koi fish', 'emperor', 'temple'],
    previewImage: '/themes/asian-dynasty.avif',
    moodImage: 'https://placehold.co/600x400/EFE1C6/000000/png?text=Asian+Dynasty'
  },
  {
    id: 'fantasy-kingdom',
    name: 'Fantasy Kingdom',
    description: 'Medieval fantasy realm with knights and dragons',
    colors: {
      primary: '#4B0082',
      secondary: '#FFD700',
      accent: '#B22222',
      background: '#483D8B'
    },
    keywords: ['dragons', 'castle', 'kingdom', 'knight', 'princess', 'magic sword'],
    symbolIdeas: ['dragon', 'crown', 'castle', 'magic sword', 'shield', 'wizard'],
    previewImage: '/themes/fantasy-kingdom.avif',
    moodImage: 'https://placehold.co/600x400/483D8B/FFFFFF/png?text=Fantasy+Kingdom'
  },
  {
    id: 'futuristic-city',
    name: 'Futuristic City',
    description: 'Neon-lit cyberpunk metropolis of tomorrow',
    colors: {
      primary: '#00FFFF',
      secondary: '#FF00FF',
      accent: '#7B68EE',
      background: '#0C0C0C'
    },
    keywords: ['cyberpunk', 'neon', 'futuristic', 'robots', 'holograms', 'skyscrapers'],
    symbolIdeas: ['robot', 'flying car', 'neon sign', 'hologram', 'cyborg', 'microchip'],
    previewImage: '/themes/futuristic-city.avif',
    moodImage: 'https://placehold.co/600x400/0C0C0C/00FFFF/png?text=Futuristic+City'
  },
  {
    id: 'tropical-paradise',
    name: 'Tropical Paradise',
    description: 'Exotic beach vacation with palm trees and cocktails',
    colors: {
      primary: '#FF6347',
      secondary: '#32CD32',
      accent: '#1E90FF',
      background: '#87CEEB'
    },
    keywords: ['beach', 'palm trees', 'island', 'cocktails', 'sunshine', 'tropical fruits'],
    symbolIdeas: ['coconut', 'palm tree', 'pineapple', 'cocktail', 'flamingo', 'beach umbrella'],
    previewImage: '/themes/tropical-paradise.avif',
    moodImage: 'https://placehold.co/600x400/87CEEB/FFFFFF/png?text=Tropical+Paradise'
  },
  {
    id: 'golden-vegas',
    name: 'Golden Vegas',
    description: 'Glitzy casino atmosphere with lights and luxury',
    colors: {
      primary: '#FFD700',
      secondary: '#8B0000',
      accent: '#4B0082',
      background: '#000000'
    },
    keywords: ['casino', 'jackpot', 'luxury', 'gambling', 'vegas strip', 'neon lights'],
    symbolIdeas: ['dice', 'playing card', 'roulette', 'dollar sign', 'diamond', 'lucky seven'],
    previewImage: '/themes/golden-vegas.avif',
    moodImage: 'https://placehold.co/600x400/000000/FFD700/png?text=Golden+Vegas'
  },
  {
    id: 'ancient-aztec',
    name: 'Ancient Aztec',
    description: 'Lost temples and Mesoamerican treasures',
    colors: {
      primary: '#CD853F',
      secondary: '#006400',
      accent: '#B22222',
      background: '#8B4513'
    },
    keywords: ['temple', 'gold', 'jungle', 'treasure', 'ancient civilization', 'rituals'],
    symbolIdeas: ['golden mask', 'sun stone', 'jaguar', 'temple', 'golden idol', 'ancient calendar'],
    previewImage: '/themes/ancient-aztec.avif',
    moodImage: 'https://placehold.co/600x400/8B4513/FFFFFF/png?text=Ancient+Aztec'
  },
  {
    id: 'candy-land',
    name: 'Candy Land',
    description: 'Sweet treats and colorful confectionery world',
    colors: {
      primary: '#FF69B4',
      secondary: '#00BFFF',
      accent: '#ADFF2F',
      background: '#FFCCFF'
    },
    keywords: ['candy', 'sweets', 'chocolate', 'lollipop', 'gummy', 'dessert'],
    symbolIdeas: ['lollipop', 'candy cane', 'chocolate bar', 'cupcake', 'gummy bear', 'ice cream'],
    previewImage: '/themes/candy-land.avif',
    moodImage: 'https://placehold.co/600x400/FFCCFF/000000/png?text=Candy+Land'
  },
  {
    id: 'other',
    name: 'Other',
    description: 'Other Theme',
    colors: {
      primary: '#FF69B4',
      secondary: '#00BFFF',
      accent: '#ADFF2F',
      background: '#FFCCFF'
    },
    keywords: ['other'],
    symbolIdeas: ['other'],
    previewImage: '/themes/other.avif',
    moodImage: 'https://placehold.co/600x400/FFCCFF/000000/png?text=Other'
  }
];