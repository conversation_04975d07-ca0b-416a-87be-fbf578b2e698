/**
 * Sprite Sheet Validation and Cutting Utilities
 * Ensures proper sprite sheet format and provides precise cutting logic
 */

export interface SpriteSheetValidationResult {
  isValid: boolean;
  dimensions: {
    width: number;
    height: number;
    frameWidth: number;
    frameHeight: number;
  };
  gridInfo: {
    cols: number;
    rows: number;
    totalFrames: number;
  };
  issues: string[];
  recommendations: string[];
}

export interface SpriteFrame {
  x: number;
  y: number;
  width: number;
  height: number;
  frameIndex: number;
  row: number;
  col: number;
}

/**
 * Validate sprite sheet dimensions and grid alignment
 */
export const validateSpriteSheet = (
  imageWidth: number,
  imageHeight: number,
  gridCols: number = 5,
  gridRows: number = 5
): SpriteSheetValidationResult => {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Calculate frame dimensions
  const frameWidth = imageWidth / gridCols;
  const frameHeight = imageHeight / gridRows;
  
  // Check if dimensions are integers (pixel-perfect)
  if (frameWidth !== Math.floor(frameWidth)) {
    issues.push(`Frame width (${frameWidth}) is not a whole number. Image width should be divisible by ${gridCols}.`);
    recommendations.push(`Use image width of ${Math.floor(frameWidth) * gridCols} or ${Math.ceil(frameWidth) * gridCols} pixels.`);
  }
  
  if (frameHeight !== Math.floor(frameHeight)) {
    issues.push(`Frame height (${frameHeight}) is not a whole number. Image height should be divisible by ${gridRows}.`);
    recommendations.push(`Use image height of ${Math.floor(frameHeight) * gridRows} or ${Math.ceil(frameHeight) * gridRows} pixels.`);
  }
  
  // Check for square frames (recommended for slot symbols)
  if (Math.abs(frameWidth - frameHeight) > 1) {
    recommendations.push(`Frames are not square (${frameWidth}x${frameHeight}). Square frames are recommended for slot symbols.`);
  }
  
  // Check minimum frame size
  const minFrameSize = 64;
  if (frameWidth < minFrameSize || frameHeight < minFrameSize) {
    issues.push(`Frame size (${frameWidth}x${frameHeight}) is too small. Minimum recommended size is ${minFrameSize}x${minFrameSize}.`);
  }
  
  // Check maximum frame size (for performance)
  const maxFrameSize = 512;
  if (frameWidth > maxFrameSize || frameHeight > maxFrameSize) {
    recommendations.push(`Frame size (${frameWidth}x${frameHeight}) is very large. Consider reducing to improve performance.`);
  }
  
  // Check aspect ratio
  const aspectRatio = imageWidth / imageHeight;
  if (Math.abs(aspectRatio - 1) > 0.1) {
    recommendations.push(`Sprite sheet is not square (${imageWidth}x${imageHeight}). Square sprite sheets are easier to work with.`);
  }
  
  return {
    isValid: issues.length === 0,
    dimensions: {
      width: imageWidth,
      height: imageHeight,
      frameWidth: Math.floor(frameWidth),
      frameHeight: Math.floor(frameHeight)
    },
    gridInfo: {
      cols: gridCols,
      rows: gridRows,
      totalFrames: gridCols * gridRows
    },
    issues,
    recommendations
  };
};

/**
 * Generate precise frame coordinates for sprite cutting with proper spacing
 */
export const generateFrameCoordinates = (
  imageWidth: number,
  imageHeight: number,
  gridCols: number = 5,
  gridRows: number = 5
): SpriteFrame[] => {
  const frames: SpriteFrame[] = [];

  // Enhanced calculation for spaced sprite sheets
  const EDGE_MARGIN = 16; // Reduced margin from edges for better frame visibility
  const usableWidth = imageWidth - (2 * EDGE_MARGIN);
  const usableHeight = imageHeight - (2 * EDGE_MARGIN);

  // Calculate frame slot size (including spacing)
  const frameSlotWidth = usableWidth / gridCols;
  const frameSlotHeight = usableHeight / gridRows;

  // Calculate actual frame content size (excluding spacing)
  const SPACING_RATIO = 0.85; // 85% content, 15% spacing
  const frameContentWidth = Math.floor(frameSlotWidth * SPACING_RATIO);
  const frameContentHeight = Math.floor(frameSlotHeight * SPACING_RATIO);

  // Calculate centering offset within each slot
  const contentOffsetX = Math.floor((frameSlotWidth - frameContentWidth) / 2);
  const contentOffsetY = Math.floor((frameSlotHeight - frameContentHeight) / 2);

  let frameIndex = 0;

  console.log(`📐 Enhanced frame calculation:
    - Image size: ${imageWidth}x${imageHeight}
    - Edge margin: ${EDGE_MARGIN}px
    - Usable area: ${usableWidth}x${usableHeight}
    - Frame slot size: ${frameSlotWidth.toFixed(1)}x${frameSlotHeight.toFixed(1)}
    - Frame content size: ${frameContentWidth}x${frameContentHeight}
    - Content offset: ${contentOffsetX}x${contentOffsetY}`);

  for (let row = 0; row < gridRows; row++) {
    for (let col = 0; col < gridCols; col++) {
      // Calculate slot position
      const slotX = EDGE_MARGIN + (col * frameSlotWidth);
      const slotY = EDGE_MARGIN + (row * frameSlotHeight);

      // Calculate actual frame position (centered in slot)
      const x = Math.floor(slotX + contentOffsetX);
      const y = Math.floor(slotY + contentOffsetY);

      // Ensure we don't exceed image bounds
      const actualWidth = Math.min(frameContentWidth, imageWidth - x);
      const actualHeight = Math.min(frameContentHeight, imageHeight - y);

      frames.push({
        x,
        y,
        width: actualWidth,
        height: actualHeight,
        frameIndex,
        row,
        col
      });

      // Debug log for first few frames
      if (frameIndex < 3) {
        console.log(`🎬 Frame ${frameIndex + 1}: Slot(${slotX.toFixed(1)},${slotY.toFixed(1)}) → Content(${x},${y}) ${actualWidth}x${actualHeight}`);
      }

      frameIndex++;
    }
  }

  return frames;
};

/**
 * Analyze sprite sheet for common issues
 */
export const analyzeSpriteSheet = async (imageUrl: string): Promise<{
  analysis: SpriteSheetValidationResult;
  frames: SpriteFrame[];
  debugInfo: {
    loadTime: number;
    imageFormat: string;
    hasTransparency: boolean;
  };
}> => {
  const startTime = Date.now();
  
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      const loadTime = Date.now() - startTime;
      
      // Create canvas to analyze the image
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Could not create canvas context'));
        return;
      }
      
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      
      // Check for transparency
      const imageData = ctx.getImageData(0, 0, img.width, img.height);
      let hasTransparency = false;
      
      for (let i = 3; i < imageData.data.length; i += 4) {
        if (imageData.data[i] < 255) {
          hasTransparency = true;
          break;
        }
      }
      
      // Validate sprite sheet
      const analysis = validateSpriteSheet(img.width, img.height);
      const frames = generateFrameCoordinates(img.width, img.height);
      
      resolve({
        analysis,
        frames,
        debugInfo: {
          loadTime,
          imageFormat: imageUrl.includes('.png') ? 'PNG' : 
                      imageUrl.includes('.jpg') || imageUrl.includes('.jpeg') ? 'JPEG' : 'Unknown',
          hasTransparency
        }
      });
    };
    
    img.onerror = () => {
      reject(new Error(`Failed to load image: ${imageUrl}`));
    };
    
    img.src = imageUrl;
  });
};

/**
 * Generate debug visualization of sprite sheet cutting
 */
export const generateDebugVisualization = (
  frames: SpriteFrame[],
  imageWidth: number,
  imageHeight: number
): string => {
  let visualization = `Sprite Sheet Debug (${imageWidth}x${imageHeight}):\n`;
  visualization += `${'─'.repeat(50)}\n`;
  
  frames.forEach((frame, index) => {
    visualization += `Frame ${index.toString().padStart(2, '0')}: `;
    visualization += `(${frame.x.toString().padStart(3, ' ')},${frame.y.toString().padStart(3, ' ')}) `;
    visualization += `${frame.width}x${frame.height} `;
    visualization += `[Row ${frame.row}, Col ${frame.col}]\n`;
  });
  
  return visualization;
};

/**
 * Recommended sprite sheet generation settings
 */
export const SPRITE_SHEET_RECOMMENDATIONS = {
  // Optimal dimensions for 5x5 grid
  OPTIMAL_SIZES: [
    { width: 1000, height: 1000, frameSize: 200 },
    { width: 1024, height: 1024, frameSize: 204.8 },
    { width: 1280, height: 1280, frameSize: 256 },
    { width: 1600, height: 1600, frameSize: 320 }
  ],
  
  // Grid configurations
  GRID_CONFIGS: [
    { cols: 5, rows: 5, frames: 25, name: 'Standard 5x5' },
    { cols: 4, rows: 4, frames: 16, name: 'Compact 4x4' },
    { cols: 6, rows: 6, frames: 36, name: 'Extended 6x6' },
    { cols: 8, rows: 4, frames: 32, name: 'Wide 8x4' }
  ],
  
  // Quality settings
  QUALITY_SETTINGS: {
    MIN_FRAME_SIZE: 64,
    RECOMMENDED_FRAME_SIZE: 200,
    MAX_FRAME_SIZE: 512,
    PREFERRED_FORMAT: 'PNG',
    TRANSPARENCY_REQUIRED: true
  }
};
