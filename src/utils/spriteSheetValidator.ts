/**
 * Sprite Sheet Validation and Cutting Utilities
 * Ensures proper sprite sheet format and provides precise cutting logic
 */

import {
  COLS,
  ROWS,
  SYMBOL_SIZE,
  GUTTER,
  OUTER_MARGIN,
  CELL_SIZE,
  SPRITE_WIDTH,
  SPRITE_HEIGHT
} from './spriteSheetConstants';

export interface SpriteSheetValidationResult {
  isValid: boolean;
  dimensions: {
    width: number;
    height: number;
    frameWidth: number;
    frameHeight: number;
  };
  gridInfo: {
    cols: number;
    rows: number;
    totalFrames: number;
  };
  issues: string[];
  recommendations: string[];
}

export interface SpriteFrame {
  x: number;
  y: number;
  width: number;
  height: number;
  frameIndex: number;
  row: number;
  col: number;
}

/**
 * Validate sprite sheet dimensions and grid alignment
 */
export const validateSpriteSheet = (
  imageWidth: number,
  imageHeight: number,
  gridCols: number = 5,
  gridRows: number = 5
): SpriteSheetValidationResult => {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Calculate frame dimensions
  const frameWidth = imageWidth / gridCols;
  const frameHeight = imageHeight / gridRows;
  
  // Check if dimensions are integers (pixel-perfect)
  if (frameWidth !== Math.floor(frameWidth)) {
    issues.push(`Frame width (${frameWidth}) is not a whole number. Image width should be divisible by ${gridCols}.`);
    recommendations.push(`Use image width of ${Math.floor(frameWidth) * gridCols} or ${Math.ceil(frameWidth) * gridCols} pixels.`);
  }
  
  if (frameHeight !== Math.floor(frameHeight)) {
    issues.push(`Frame height (${frameHeight}) is not a whole number. Image height should be divisible by ${gridRows}.`);
    recommendations.push(`Use image height of ${Math.floor(frameHeight) * gridRows} or ${Math.ceil(frameHeight) * gridRows} pixels.`);
  }
  
  // Check for square frames (recommended for slot symbols)
  if (Math.abs(frameWidth - frameHeight) > 1) {
    recommendations.push(`Frames are not square (${frameWidth}x${frameHeight}). Square frames are recommended for slot symbols.`);
  }
  
  // Check minimum frame size
  const minFrameSize = 64;
  if (frameWidth < minFrameSize || frameHeight < minFrameSize) {
    issues.push(`Frame size (${frameWidth}x${frameHeight}) is too small. Minimum recommended size is ${minFrameSize}x${minFrameSize}.`);
  }
  
  // Check maximum frame size (for performance)
  const maxFrameSize = 512;
  if (frameWidth > maxFrameSize || frameHeight > maxFrameSize) {
    recommendations.push(`Frame size (${frameWidth}x${frameHeight}) is very large. Consider reducing to improve performance.`);
  }
  
  // Check aspect ratio
  const aspectRatio = imageWidth / imageHeight;
  if (Math.abs(aspectRatio - 1) > 0.1) {
    recommendations.push(`Sprite sheet is not square (${imageWidth}x${imageHeight}). Square sprite sheets are easier to work with.`);
  }
  
  return {
    isValid: issues.length === 0,
    dimensions: {
      width: imageWidth,
      height: imageHeight,
      frameWidth: Math.floor(frameWidth),
      frameHeight: Math.floor(frameHeight)
    },
    gridInfo: {
      cols: gridCols,
      rows: gridRows,
      totalFrames: gridCols * gridRows
    },
    issues,
    recommendations
  };
};

/**
 * Compute frame rectangles for a perfectly aligned 5×5 sprite sheet.
 * Throws if the sheet dimensions don't match expected constants.
 */
export const generateFrameCoordinates = (
  imageWidth: number,
  imageHeight: number,
  gridCols: number = 5,
  gridRows: number = 5
): SpriteFrame[] => {
  // Validate that we're using the expected grid size
  if (gridCols !== COLS || gridRows !== ROWS) {
    console.warn(`⚠️ Grid size mismatch: expected ${COLS}x${ROWS}, got ${gridCols}x${gridRows}`);
  }

  // Check if dimensions match our expected sprite sheet size
  const expectedWidth = SPRITE_WIDTH;
  const expectedHeight = SPRITE_HEIGHT;

  if (imageWidth !== expectedWidth || imageHeight !== expectedHeight) {
    console.warn(
      `⚠️ Sprite sheet size mismatch: ${imageWidth}×${imageHeight}. ` +
      `Expected ${expectedWidth}×${expectedHeight}. Using adaptive calculation.`
    );

    // Fall back to adaptive calculation for non-standard sizes
    return generateAdaptiveFrameCoordinates(imageWidth, imageHeight, gridCols, gridRows);
  }

  // Use precise calculation for standard sprite sheets
  const frames: SpriteFrame[] = [];
  let frameIndex = 0;

  console.log(`📐 Using precise frame calculation:
    - Image size: ${imageWidth}x${imageHeight} ✓
    - Grid: ${COLS}x${ROWS}
    - Symbol size: ${SYMBOL_SIZE}x${SYMBOL_SIZE}px
    - Cell size: ${CELL_SIZE}px (includes ${GUTTER}px gutter)
    - Outer margin: ${OUTER_MARGIN}px`);

  for (let row = 0; row < ROWS; row++) {
    for (let col = 0; col < COLS; col++) {
      const x = OUTER_MARGIN + col * CELL_SIZE;
      const y = OUTER_MARGIN + row * CELL_SIZE;

      frames.push({
        frameIndex,
        row,
        col,
        x,
        y,
        width: SYMBOL_SIZE,
        height: SYMBOL_SIZE
      });

      // Debug log for first few frames
      if (frameIndex < 3) {
        console.log(`🎬 Frame ${frameIndex + 1}: Position(${x},${y}) Size: ${SYMBOL_SIZE}x${SYMBOL_SIZE}`);
      }

      frameIndex++;
    }
  }

  return frames;
};

/**
 * Fallback adaptive frame calculation for non-standard sprite sheet sizes
 */
const generateAdaptiveFrameCoordinates = (
  imageWidth: number,
  imageHeight: number,
  gridCols: number,
  gridRows: number
): SpriteFrame[] => {
  const frames: SpriteFrame[] = [];

  // Use simple grid division for non-standard sizes
  const frameWidth = Math.floor(imageWidth / gridCols);
  const frameHeight = Math.floor(imageHeight / gridRows);

  // Add small padding to avoid cutting adjacent frames
  const PADDING = 10;
  const actualFrameWidth = frameWidth - (2 * PADDING);
  const actualFrameHeight = frameHeight - (2 * PADDING);

  let frameIndex = 0;

  console.log(`📐 Using adaptive frame calculation:
    - Image size: ${imageWidth}x${imageHeight}
    - Grid: ${gridCols}x${gridRows}
    - Frame size: ${frameWidth}x${frameHeight}
    - Padding: ${PADDING}px
    - Actual frame size: ${actualFrameWidth}x${actualFrameHeight}`);

  for (let row = 0; row < gridRows; row++) {
    for (let col = 0; col < gridCols; col++) {
      const x = Math.floor((col * frameWidth) + PADDING);
      const y = Math.floor((row * frameHeight) + PADDING);

      const actualWidth = Math.min(actualFrameWidth, imageWidth - x);
      const actualHeight = Math.min(actualFrameHeight, imageHeight - y);

      frames.push({
        x,
        y,
        width: actualWidth,
        height: actualHeight,
        frameIndex,
        row,
        col
      });

      frameIndex++;
    }
  }

  return frames;
};

/**
 * Analyze sprite sheet for common issues
 */
export const analyzeSpriteSheet = async (imageUrl: string): Promise<{
  analysis: SpriteSheetValidationResult;
  frames: SpriteFrame[];
  debugInfo: {
    loadTime: number;
    imageFormat: string;
    hasTransparency: boolean;
  };
}> => {
  const startTime = Date.now();
  
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      const loadTime = Date.now() - startTime;
      
      // Create canvas to analyze the image
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Could not create canvas context'));
        return;
      }
      
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      
      // Check for transparency
      const imageData = ctx.getImageData(0, 0, img.width, img.height);
      let hasTransparency = false;
      
      for (let i = 3; i < imageData.data.length; i += 4) {
        if (imageData.data[i] < 255) {
          hasTransparency = true;
          break;
        }
      }
      
      // Validate sprite sheet
      const analysis = validateSpriteSheet(img.width, img.height);
      const frames = generateFrameCoordinates(img.width, img.height);
      
      resolve({
        analysis,
        frames,
        debugInfo: {
          loadTime,
          imageFormat: imageUrl.includes('.png') ? 'PNG' : 
                      imageUrl.includes('.jpg') || imageUrl.includes('.jpeg') ? 'JPEG' : 'Unknown',
          hasTransparency
        }
      });
    };
    
    img.onerror = () => {
      reject(new Error(`Failed to load image: ${imageUrl}`));
    };
    
    img.src = imageUrl;
  });
};

/**
 * Generate debug visualization of sprite sheet cutting
 */
export const generateDebugVisualization = (
  frames: SpriteFrame[],
  imageWidth: number,
  imageHeight: number
): string => {
  let visualization = `Sprite Sheet Debug (${imageWidth}x${imageHeight}):\n`;
  visualization += `${'─'.repeat(50)}\n`;
  
  frames.forEach((frame, index) => {
    visualization += `Frame ${index.toString().padStart(2, '0')}: `;
    visualization += `(${frame.x.toString().padStart(3, ' ')},${frame.y.toString().padStart(3, ' ')}) `;
    visualization += `${frame.width}x${frame.height} `;
    visualization += `[Row ${frame.row}, Col ${frame.col}]\n`;
  });
  
  return visualization;
};

/**
 * Recommended sprite sheet generation settings
 */
export const SPRITE_SHEET_RECOMMENDATIONS = {
  // Optimal dimensions for 5x5 grid
  OPTIMAL_SIZES: [
    { width: 1000, height: 1000, frameSize: 200 },
    { width: 1024, height: 1024, frameSize: 204.8 },
    { width: 1280, height: 1280, frameSize: 256 },
    { width: 1600, height: 1600, frameSize: 320 }
  ],
  
  // Grid configurations
  GRID_CONFIGS: [
    { cols: 5, rows: 5, frames: 25, name: 'Standard 5x5' },
    { cols: 4, rows: 4, frames: 16, name: 'Compact 4x4' },
    { cols: 6, rows: 6, frames: 36, name: 'Extended 6x6' },
    { cols: 8, rows: 4, frames: 32, name: 'Wide 8x4' }
  ],
  
  // Quality settings
  QUALITY_SETTINGS: {
    MIN_FRAME_SIZE: 64,
    RECOMMENDED_FRAME_SIZE: 200,
    MAX_FRAME_SIZE: 512,
    PREFERRED_FORMAT: 'PNG',
    TRANSPARENCY_REQUIRED: true
  }
};
