/**
 * Sprite Sheet Generator for Animation Lab
 * Generates 5x5 sprite sheets for animated symbols using OpenAI API
 */

import { enhancedOpenaiClient } from './enhancedOpenaiClient';

export interface SpriteSheetConfig {
  prompt: string;
  symbolType: 'block' | 'contour';
  contentType: 'symbol-only' | 'symbol-wild' | 'symbol-scatter' | 'symbol-bonus' | 'symbol-free' | 'symbol-jackpot' | 'text-only';
  animationComplexity: 'simple' | 'medium' | 'complex';
  layoutTemplate?: 'text-top' | 'text-bottom' | 'text-overlay';
}

export interface SpriteSheetResult {
  success: boolean;
  spriteSheetUrl?: string;
  error?: string;
}

/**
 * Generate animation prompts based on complexity level
 */
const getAnimationDescription = (complexity: 'simple' | 'medium' | 'complex'): string => {
  const descriptions = {
    simple: 'gentle floating motion, subtle glow variations, soft breathing effect',
    medium: 'dynamic movement with rotation, pulsing energy, color shifts, particle effects',
    complex: 'elaborate transformation sequence, magical effects, multiple animation layers, dramatic lighting changes'
  };
  return descriptions[complexity];
};

/**
 * Generate content-specific animation details
 */
const getContentAnimationDetails = (contentType: string): string => {
  const contentAnimations = {
    'symbol-wild': 'WILD text glowing and pulsing, symbol radiating energy',
    'symbol-scatter': 'SCATTER text sparkling, symbol with magical aura',
    'symbol-bonus': 'BONUS text bouncing, symbol with celebration effects',
    'symbol-free': 'FREE text floating, symbol with liberation effects',
    'symbol-jackpot': 'JACKPOT text shimmering, symbol with golden rays',
    'symbol-only': 'symbol with natural movement and energy',
    'text-only': 'text with dynamic typography effects'
  };
  return contentAnimations[contentType] || 'symbol with smooth animation';
};

/**
 * Generate a 5x5 sprite sheet for animation
 */
export const generateSpriteSheet = async (config: SpriteSheetConfig): Promise<SpriteSheetResult> => {
  try {
    console.log('🎬 Generating 5x5 sprite sheet for animation...');
    
    const animationDesc = getAnimationDescription(config.animationComplexity);
    const contentAnimationDesc = getContentAnimationDetails(config.contentType);
    
    // Create enhanced prompt for sprite sheet generation with proper spacing
    const spriteSheetPrompt = `
Create a PRECISE 5x5 grid sprite sheet (25 frames total) for slot machine symbol animation with PROPER SPACING:

CRITICAL GRID REQUIREMENTS WITH SPACING:
- EXACT 5x5 grid layout with CONSISTENT SPACING between frames
- Add 10-pixel transparent padding between each frame for clean separation
- Each frame content area should be 180x180 pixels with 10px spacing around it
- Total image should be 1024x1024 pixels with proper frame distribution
- Frame layout: 5 columns × 5 rows with equal spacing throughout
- IMPORTANT: Leave 16px margin from all edges to prevent cutting
- Each frame boundary should be clearly defined with transparent spacing

FRAME DISTRIBUTION CALCULATION:
- Total canvas: 1024x1024 pixels
- Margin from edges: 16px on all sides
- Usable area: 992x992 pixels
- Frame size including spacing: 196.8x196.8 pixels per frame slot
- Actual symbol area per frame: 180x180 pixels
- Spacing between frames: 16.8px transparent gap

SYMBOL DESCRIPTION: ${config.prompt}

BACKGROUND REQUIREMENTS:
- COMPLETELY TRANSPARENT background for all frames and it's important that the background should be transparent
- NO solid colors, NO gradients in background areas just transparent
- Only the symbol/text should be visible, everything else transparent
- Use PNG format with alpha channel transparency
- Background must be 100% transparent (alpha = 0)

SYMBOL CONSISTENCY:
- IDENTICAL symbol size and position in each frame
- Symbol should occupy roughly 70-80% of each frame area
- Symbol must be centered in each frame
- NO size variations between frames - only animation effects should change
- Maintain exact same symbol proportions across all 25 frames

ANIMATION TYPE: ${animationDesc}
CONTENT ANIMATION: ${contentAnimationDesc}

FRAME PROGRESSION (with consistent sizing and proper spacing):
- Frames 1-5 (TOP ROW): Starting pose, positioned at Y: 32-212px, SAME SIZE
- Frames 6-10 (SECOND ROW): Building energy, positioned at Y: 230-410px, SAME SIZE
- Frames 11-15 (MIDDLE ROW): Peak animation, positioned at Y: 428-608px, SAME SIZE
- Frames 16-20 (FOURTH ROW): Transitioning back, positioned at Y: 626-806px, SAME SIZE
- Frames 21-25 (BOTTOM ROW): Return to start, positioned at Y: 824-1004px, SAME SIZE

CRITICAL BOTTOM ROW REQUIREMENTS:
- Frame 21 (bottom-left): Must be fully visible at position (32, 824) with full 180x180 size
- Frame 25 (bottom-right): Must be fully visible at position (832, 824) with full 180x180 size
- NEVER cut off or crop the bottom row - ensure full 180px height for all bottom frames
- Leave at least 20px transparent space below the bottom row (1024 - 1004 = 20px margin)

SPRITE CUTTING COMPATIBILITY WITH SPACING:
- Each frame content must be centered within its allocated space
- Frame content area: 180x180 pixels per frame
- Frame positions with 12px edge margin and proper spacing:
  * Row 1: Y positions around 29-209px
  * Row 2: Y positions around 227-407px
  * Row 3: Y positions around 425-605px
  * Row 4: Y positions around 623-803px
  * Row 5: Y positions around 821-1001px
- Column spacing follows same pattern horizontally
- CRITICAL: Ensure bottom row (Row 5) has full 180px height and doesn't get cut off
- Leave adequate transparent space around each symbol for clean extraction

TECHNICAL REQUIREMENTS:
- ${config.symbolType === 'block' ? 'Square symbol format with transparent background' : 'Custom shape with transparent background'}
- ${config.layoutTemplate ? `Text layout: ${config.layoutTemplate}` : 'Integrated text and symbol design'}

- Professional game art quality with clean, sharp edges
- NO background elements, NO environmental effects
- Focus only on the symbol/text animation

ANIMATION CONTINUITY:
- Frame 25 should smoothly connect back to frame 1 for seamless looping
- Maintain consistent lighting and color palette across all frames
- Smooth progression between adjacent frames
- NEVER change symbol size or position - only animate effects, glow, rotation, etc.

Style: Professional slot machine game art, vibrant colors, transparent background, pixel-perfect grid alignment, high quality digital illustration optimized for sprite sheet cutting.
`;

    console.log('🎨 Sprite sheet prompt:', spriteSheetPrompt.substring(0, 200) + '...');

    // Generate the sprite sheet using OpenAI with optimal settings
    const response = await enhancedOpenaiClient.generateImageWithConfig({
      prompt: spriteSheetPrompt,
      targetSymbolId: `spritesheet_${Date.now()}`,
      gameId: 'animation-lab-sprites'
    });

    if (response.success && response.images && response.images.length > 0) {
      console.log('✅ Sprite sheet generated successfully');
      return {
        success: true,
        spriteSheetUrl: response.images[0]
      };
    } else {
      throw new Error(response.error || 'Failed to generate sprite sheet');
    }

  } catch (error) {
    console.error('❌ Sprite sheet generation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Extract individual frames from a 5x5 sprite sheet
 * This function will be used by the PixiJS animation system
 */
export const extractFramesFromSpriteSheet = (
  spriteSheetTexture: any, // PIXI.Texture
  frameWidth: number,
  frameHeight: number
): any[] => {
  const frames = [];
  
  // Extract 25 frames from 5x5 grid
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      const frameTexture = new (window as any).PIXI.Texture(
        spriteSheetTexture,
        new (window as any).PIXI.Rectangle(
          col * frameWidth,
          row * frameHeight,
          frameWidth,
          frameHeight
        )
      );
      frames.push(frameTexture);
    }
  }
  
  return frames;
};
