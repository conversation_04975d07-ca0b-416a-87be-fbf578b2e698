/**
 * Sprite Sheet Generator for Animation Lab
 * Generates 5x5 sprite sheets for animated symbols using OpenAI API
 */

import { enhancedOpenaiClient } from './enhancedOpenaiClient';

export interface SpriteSheetConfig {
  prompt: string;
  symbolType: 'block' | 'contour';
  contentType: 'symbol-only' | 'symbol-wild' | 'symbol-scatter' | 'symbol-bonus' | 'symbol-free' | 'symbol-jackpot' | 'text-only';
  animationComplexity: 'simple' | 'medium' | 'complex';
  layoutTemplate?: 'text-top' | 'text-bottom' | 'text-overlay';
}

export interface SpriteSheetResult {
  success: boolean;
  spriteSheetUrl?: string;
  error?: string;
}

/**
 * Generate animation prompts based on complexity level
 */
const getAnimationDescription = (complexity: 'simple' | 'medium' | 'complex'): string => {
  const descriptions = {
    simple: 'gentle floating motion, subtle glow variations, soft breathing effect',
    medium: 'dynamic movement with rotation, pulsing energy, color shifts, particle effects',
    complex: 'elaborate transformation sequence, magical effects, multiple animation layers, dramatic lighting changes'
  };
  return descriptions[complexity];
};

/**
 * Generate content-specific animation details
 */
const getContentAnimationDetails = (contentType: string): string => {
  const contentAnimations = {
    'symbol-wild': 'WILD text glowing and pulsing, symbol radiating energy',
    'symbol-scatter': 'SCATTER text sparkling, symbol with magical aura',
    'symbol-bonus': 'BONUS text bouncing, symbol with celebration effects',
    'symbol-free': 'FREE text floating, symbol with liberation effects',
    'symbol-jackpot': 'JACKPOT text shimmering, symbol with golden rays',
    'symbol-only': 'symbol with natural movement and energy',
    'text-only': 'text with dynamic typography effects'
  };
  return contentAnimations[contentType] || 'symbol with smooth animation';
};

/**
 * Generate a 5x5 sprite sheet for animation
 */
export const generateSpriteSheet = async (config: SpriteSheetConfig): Promise<SpriteSheetResult> => {
  try {
    console.log('🎬 Generating 5x5 sprite sheet for animation...');
    
    const animationDesc = getAnimationDescription(config.animationComplexity);
    const contentAnimationDesc = getContentAnimationDetails(config.contentType);
    
    // Create enhanced prompt for sprite sheet generation with precise specifications
    const spriteSheetPrompt = `
Create a PRECISE 5x5 grid sprite sheet (25 frames total) for slot machine symbol animation with EXACT specifications:

CRITICAL GRID REQUIREMENTS:
- EXACT 5x5 grid layout with NO gaps, NO borders, NO padding between frames
- Each frame MUST be exactly the same size (1/5th width, 1/5th height of total image)
- Frames arranged in perfect rows and columns with pixel-perfect alignment
- Total image should be square (1024x1024) so each frame is exactly 204.8x204.8 pixels
- NO decorative borders, NO frame separators, NO grid lines visible

SYMBOL DESCRIPTION: ${config.prompt}

BACKGROUND REQUIREMENTS:
- COMPLETELY TRANSPARENT background for all frames
- NO solid colors, NO gradients in background areas
- Only the symbol/text should be visible, everything else transparent
- Use PNG format with alpha channel transparency
- Background must be 100% transparent (alpha = 0)

SYMBOL CONSISTENCY:
- IDENTICAL symbol size and position in each frame
- Symbol should occupy roughly 70-80% of each frame area
- Symbol must be centered in each frame
- NO size variations between frames - only animation effects should change
- Maintain exact same symbol proportions across all 25 frames

ANIMATION TYPE: ${animationDesc}
CONTENT ANIMATION: ${contentAnimationDesc}

FRAME PROGRESSION (with consistent sizing):
- Frames 1-5 (top row): Starting pose with initial effects, SAME SIZE
- Frames 6-10 (second row): Building energy and movement, SAME SIZE
- Frames 11-15 (middle row): Peak animation with maximum effects, SAME SIZE
- Frames 16-20 (fourth row): Transitioning back with continued motion, SAME SIZE
- Frames 21-25 (bottom row): Return to starting pose, SAME SIZE

SPRITE CUTTING COMPATIBILITY:
- Each frame boundary must be at exact pixel coordinates
- Frame 1: (0,0) to (204,204)
- Frame 2: (204,0) to (408,204)
- Frame 3: (408,0) to (612,204)
- And so on in perfect grid alignment
- NO anti-aliasing or blending at frame boundaries
- Sharp, clean edges for precise sprite cutting

TECHNICAL REQUIREMENTS:
- ${config.symbolType === 'block' ? 'Square symbol format with transparent background' : 'Custom shape with transparent background'}
- ${config.layoutTemplate ? `Text layout: ${config.layoutTemplate}` : 'Integrated text and symbol design'}
- High contrast and vibrant colors for the symbol itself
- Professional game art quality with clean, sharp edges
- NO background elements, NO environmental effects
- Focus only on the symbol/text animation

ANIMATION CONTINUITY:
- Frame 25 should smoothly connect back to frame 1 for seamless looping
- Maintain consistent lighting and color palette across all frames
- Smooth progression between adjacent frames
- NEVER change symbol size or position - only animate effects, glow, rotation, etc.

Style: Professional slot machine game art, vibrant colors, transparent background, pixel-perfect grid alignment, high quality digital illustration optimized for sprite sheet cutting.
`;

    console.log('🎨 Sprite sheet prompt:', spriteSheetPrompt.substring(0, 200) + '...');

    // Generate the sprite sheet using OpenAI with optimal settings
    const response = await enhancedOpenaiClient.generateImageWithConfig({
      prompt: spriteSheetPrompt,
      targetSymbolId: `spritesheet_${Date.now()}`,
      gameId: 'animation-lab-sprites'
    });

    if (response.success && response.images && response.images.length > 0) {
      console.log('✅ Sprite sheet generated successfully');
      return {
        success: true,
        spriteSheetUrl: response.images[0]
      };
    } else {
      throw new Error(response.error || 'Failed to generate sprite sheet');
    }

  } catch (error) {
    console.error('❌ Sprite sheet generation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Extract individual frames from a 5x5 sprite sheet
 * This function will be used by the PixiJS animation system
 */
export const extractFramesFromSpriteSheet = (
  spriteSheetTexture: any, // PIXI.Texture
  frameWidth: number,
  frameHeight: number
): any[] => {
  const frames = [];
  
  // Extract 25 frames from 5x5 grid
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      const frameTexture = new (window as any).PIXI.Texture(
        spriteSheetTexture,
        new (window as any).PIXI.Rectangle(
          col * frameWidth,
          row * frameHeight,
          frameWidth,
          frameHeight
        )
      );
      frames.push(frameTexture);
    }
  }
  
  return frames;
};
