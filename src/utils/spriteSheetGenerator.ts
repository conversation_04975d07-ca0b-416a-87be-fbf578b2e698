/**
 * Sprite Sheet Generator for Animation Lab
 * Generates 5x5 sprite sheets for animated symbols using OpenAI API
 */

import { enhancedOpenaiClient } from './enhancedOpenaiClient';
import { SPRITE_WIDTH, SPRITE_HEIGHT, SYMBOL_SIZE, GUTTER, OUTER_MARGIN } from './spriteSheetConstants';

export interface SpriteSheetConfig {
  prompt: string;
  symbolType: 'block' | 'contour';
  contentType: 'symbol-only' | 'symbol-wild' | 'symbol-scatter' | 'symbol-bonus' | 'symbol-free' | 'symbol-jackpot' | 'text-only';
  animationComplexity: 'simple' | 'medium' | 'complex';
  layoutTemplate?: 'text-top' | 'text-bottom' | 'text-overlay';
}

export interface SpriteSheetResult {
  success: boolean;
  spriteSheetUrl?: string;
  error?: string;
}

/**
 * Generate animation prompts based on complexity level
 */
const getAnimationDescription = (complexity: 'simple' | 'medium' | 'complex'): string => {
  const descriptions = {
    simple: 'gentle floating motion, subtle glow variations, soft breathing effect',
    medium: 'dynamic movement with rotation, pulsing energy, color shifts, particle effects',
    complex: 'elaborate transformation sequence, magical effects, multiple animation layers, dramatic lighting changes'
  };
  return descriptions[complexity];
};

/**
 * Generate content-specific animation details
 */
const getContentAnimationDetails = (contentType: string): string => {
  const contentAnimations = {
    'symbol-wild': 'WILD text glowing and pulsing, symbol radiating energy',
    'symbol-scatter': 'SCATTER text sparkling, symbol with magical aura',
    'symbol-bonus': 'BONUS text bouncing, symbol with celebration effects',
    'symbol-free': 'FREE text floating, symbol with liberation effects',
    'symbol-jackpot': 'JACKPOT text shimmering, symbol with golden rays',
    'symbol-only': 'symbol with natural movement and energy',
    'text-only': 'text with dynamic typography effects'
  };
  return contentAnimations[contentType] || 'symbol with smooth animation';
};

/**
 * Generate a 5x5 sprite sheet for animation
 */
export const generateSpriteSheet = async (config: SpriteSheetConfig): Promise<SpriteSheetResult> => {
  try {
    console.log('🎬 Generating 5x5 sprite sheet for animation...');
    
    const animationDesc = getAnimationDescription(config.animationComplexity);
    const contentAnimationDesc = getContentAnimationDetails(config.contentType);
    
    // Create enhanced prompt for sprite sheet generation with precise dimensions
    const spriteSheetPrompt = `
Create a PRECISE 5x5 grid sprite sheet (25 frames total) for slot machine symbol animation with EXACT SPECIFICATIONS:

CRITICAL GRID REQUIREMENTS:
- EXACT 5x5 grid layout with CONSISTENT SPACING between frames
- Total image dimensions: ${SPRITE_WIDTH}x${SPRITE_HEIGHT} pixels (EXACT - this is 972x972)
- Each symbol area: ${SYMBOL_SIZE}x${SYMBOL_SIZE} pixels (EXACT - this is 180x180)
- Spacing between frames: ${GUTTER}px transparent gap (EXACT - this is 10px)
- Outer margin from all edges: ${OUTER_MARGIN}px (EXACT - this is 16px)
- Frame layout: 5 columns × 5 rows with precise positioning

PRECISE FRAME POSITIONING:
- Frame positions calculated as: ${OUTER_MARGIN}px + (column/row * ${SYMBOL_SIZE + GUTTER}px)
- Column positions: 16px, 206px, 396px, 586px, 776px
- Row positions: 16px, 206px, 396px, 586px, 776px
- Each frame must be exactly ${SYMBOL_SIZE}x${SYMBOL_SIZE} pixels
- Transparent spacing of ${GUTTER}px between each frame
- ${OUTER_MARGIN}px transparent margin from all edges

SYMBOL DESCRIPTION: ${config.prompt}

BACKGROUND REQUIREMENTS:
- COMPLETELY TRANSPARENT background for all frames - this is CRITICAL
- NO solid colors, NO gradients, NO frames, NO borders in background areas
- NO decorative frames or borders around symbols - only pure transparent background
- Only the symbol/text should be visible, everything else must be transparent
- Use PNG format with alpha channel transparency
- Background must be 100% transparent (alpha = 0)
- IMPORTANT: Do not add any frames, borders, or background elements around the symbols

SYMBOL CONSISTENCY:
- IDENTICAL symbol size and position in each frame
- Symbol should occupy roughly 70-80% of each frame area
- Symbol must be centered in each frame
- NO size variations between frames - only animation effects should change
- Maintain exact same symbol proportions across all 25 frames

ANIMATION TYPE: ${animationDesc}
CONTENT ANIMATION: ${contentAnimationDesc}

FRAME PROGRESSION (with precise positioning):
- Frames 1-5 (TOP ROW): Starting pose, positioned at Y: 16-196px, SAME SIZE
- Frames 6-10 (SECOND ROW): Building energy, positioned at Y: 206-386px, SAME SIZE
- Frames 11-15 (MIDDLE ROW): Peak animation, positioned at Y: 396-576px, SAME SIZE
- Frames 16-20 (FOURTH ROW): Transitioning back, positioned at Y: 586-766px, SAME SIZE
- Frames 21-25 (BOTTOM ROW): Return to start, positioned at Y: 776-956px, SAME SIZE

CRITICAL BOTTOM ROW REQUIREMENTS:
- Frame 21 (bottom-left): Must be fully visible at position (16, 776) with full 180x180 size
- Frame 25 (bottom-right): Must be fully visible at position (776, 776) with full 180x180 size
- NEVER cut off or crop the bottom row - ensure full 180px height for all bottom frames
- Leave exactly 16px transparent space below the bottom row (972 - 956 = 16px margin)

SPRITE CUTTING COMPATIBILITY:
- Each frame content must be exactly positioned within its allocated space
- Frame content area: exactly 180x180 pixels per frame
- Precise frame positions:
  * Row 1: Y = 16px (frames end at 196px)
  * Row 2: Y = 206px (frames end at 386px)
  * Row 3: Y = 396px (frames end at 576px)
  * Row 4: Y = 586px (frames end at 766px)
  * Row 5: Y = 776px (frames end at 956px)
  * Columns: X = 16px, 206px, 396px, 586px, 776px
- CRITICAL: Ensure all frames fit exactly within the 972x972 canvas
- Each symbol must be exactly 180x180 pixels with 10px transparent gaps between frames

TECHNICAL REQUIREMENTS:
- Custom shape with COMPLETELY transparent background - no frames or borders
- ${config.layoutTemplate ? `Text layout: ${config.layoutTemplate}` : 'Integrated text and symbol design'}
- Professional game art quality with clean, sharp edges
- NO background elements, NO environmental effects, NO frames, NO borders
- NO decorative elements around the symbol - only the symbol itself
- Focus only on the symbol/text animation with transparent background
- CRITICAL: Generate only the symbol on transparent background, no additional visual elements

ANIMATION CONTINUITY:
- Frame 25 should smoothly connect back to frame 1 for seamless looping
- Maintain consistent lighting and color palette across all frames
- Smooth progression between adjacent frames
- NEVER change symbol size or position - only animate effects, glow, rotation, etc.

Style: Professional slot machine game art, vibrant colors, transparent background, pixel-perfect grid alignment, high quality digital illustration optimized for sprite sheet cutting.
`;

    console.log('🎨 Sprite sheet prompt:', spriteSheetPrompt.substring(0, 200) + '...');

    // Generate the sprite sheet using OpenAI with optimal settings
    const response = await enhancedOpenaiClient.generateImageWithConfig({
      prompt: spriteSheetPrompt,
      targetSymbolId: `spritesheet_${Date.now()}`,
      gameId: 'animation-lab-sprites'
    });

    if (response.success && response.images && response.images.length > 0) {
      console.log('✅ Sprite sheet generated successfully');
      return {
        success: true,
        spriteSheetUrl: response.images[0]
      };
    } else {
      throw new Error(response.error || 'Failed to generate sprite sheet');
    }

  } catch (error) {
    console.error('❌ Sprite sheet generation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Extract individual PIXI.Texture frames from a sprite sheet texture using precise coordinates
 */
export const extractFramesFromSpriteSheet = (
  spriteSheetTexture: any // PIXI.Texture
): any[] => {
  const PIXI = (window as any).PIXI;
  const { width, height } = spriteSheetTexture.baseTexture;

  // Validate dimensions match our constants
  if (width !== SPRITE_WIDTH || height !== SPRITE_HEIGHT) {
    console.warn(`⚠️ Sprite sheet dimensions ${width}x${height} don't match expected ${SPRITE_WIDTH}x${SPRITE_HEIGHT}`);
  }

  const frames = [];

  // Extract 25 frames from 5x5 grid using precise positioning
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      const x = OUTER_MARGIN + col * (SYMBOL_SIZE + GUTTER);
      const y = OUTER_MARGIN + row * (SYMBOL_SIZE + GUTTER);

      const frameTexture = new PIXI.Texture(
        spriteSheetTexture.baseTexture,
        new PIXI.Rectangle(x, y, SYMBOL_SIZE, SYMBOL_SIZE)
      );
      frames.push(frameTexture);
    }
  }

  return frames;
};
