/**
 * Sprite Sheet Layout Constants
 * Defines the exact layout specifications for 5x5 sprite sheet generation and cutting
 */

export const COLS = 5;
export const ROWS = 5;
export const SYMBOL_SIZE = 180;    // px of actual symbol in each frame
export const GUTTER = 10;         // px between frames
export const OUTER_MARGIN = 16;    // px around all edges

// Derived values:
export const CELL_SIZE = SYMBOL_SIZE + GUTTER;  // 190
export const SPRITE_WIDTH = (SYMBOL_SIZE * COLS) + (GUTTER * (COLS - 1)) + (OUTER_MARGIN * 2);
export const SPRITE_HEIGHT = (SYMBOL_SIZE * ROWS) + (GUTTER * (ROWS - 1)) + (OUTER_MARGIN * 2);

// Expected final dimensions: 936x936 pixels
console.log(`📐 Sprite Sheet Constants:
  - Grid: ${COLS}x${ROWS}
  - Symbol size: ${SYMBOL_SIZE}x${SYMBOL_SIZE}px
  - Gutter: ${GUTTER}px
  - Outer margin: ${OUTER_MARGIN}px
  - Cell size: ${CELL_SIZE}px
  - Total dimensions: ${SPRITE_WIDTH}x${SPRITE_HEIGHT}px`);
