import { create } from 'zustand';

interface ModalState {
  // Modal states
  isSpriteSheetGeneratorOpen: boolean;
  
  // Actions
  openSpriteSheetGenerator: () => void;
  closeSpriteSheetGenerator: () => void;
  closeAllModals: () => void;
}

export const useModalStore = create<ModalState>((set) => ({
  // Initial state
  isSpriteSheetGeneratorOpen: false,
  
  // Actions
  openSpriteSheetGenerator: () => {
    console.log('[ModalStore] Opening Sprite Sheet Generator modal');
    set({ isSpriteSheetGeneratorOpen: true });
  },
  
  closeSpriteSheetGenerator: () => {
    console.log('[ModalStore] Closing Sprite Sheet Generator modal');
    set({ isSpriteSheetGeneratorOpen: false });
  },
  
  closeAllModals: () => {
    console.log('[ModalStore] Closing all modals');
    set({
      isSpriteSheetGeneratorOpen: false,
    });
  },
}));
